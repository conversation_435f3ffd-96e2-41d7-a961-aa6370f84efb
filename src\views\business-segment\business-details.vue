<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-25 15:20:15
-->

<template>
  <div class="business-details-main">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ businessSegmentInfo[lang].title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <div class="content-main">
      <div class="content">
        <!-- 科研中心 -->
        <div class="info" style="margin: 0 calc(600/1920*100vw);">
          <div>
            <div class="title" >{{ businessSegmentInfo[lang].title }}</div>
            <div class="desc"  v-html="businessSegmentInfo[lang].homeDesc"></div>
          </div>
        </div>

        <div class="img-container1" style="margin: 0 calc(500/1920*100vw);;">
          <div class="img-view"><el-image class="img" :src="businessSegmentInfo.img" fit="cover" lazy /></div>
          <div class="sub-tag" style="margin-left: 104px; align-items: start;">
            <div class="sub-tag1">{{ businessSegmentInfo[lang].center1 }}</div>
            <div class="sub-tag2">
              <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />{{
              businessSegmentInfo[lang].center1_pos
              }}
            </div>
            <div class="sub-tag1">{{ businessSegmentInfo[lang].center2 }}</div>
            <div class="sub-tag2">
              <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />{{
              businessSegmentInfo[lang].center2_pos
              }}
            </div>
          </div>
        </div>

      </div>
      <!-- 科研板块 -->
      <div class="research-institute">
        <div style="font-size: 36px;line-height: 50px; margin:0 auto;">{{ academySciencesData[lang].title }}</div>
        <div style="font-size: 20px;line-height: 32px; margin:0 401px;">{{ academySciencesData[lang].desc }}</div>
        <div class="research-bt-div">
          <el-button v-for="item in academySciencesData[lang].children"
            :class="{'btn-research': item.id !== activeIndex, 'btn-active': item.id == activeIndex}" round="true"
            @click="btnClick(item)">
            {{ item.title }}
          </el-button>
        </div>
        <div class="research-institute-imgs" v-if="activeIndex != 2">
          <div  class="carouselItemView"  v-for="item in academySciencesData[lang].children[activeIndex].imgs"
            @click="imgClick(item)">
              <el-image :src="item.imgurl" fit="cover" style="height: 300px;" lazy />
              <div class="text-overlay" >
                {{ item.desc }}
                <div style="bottom: 20px; padding-top: 20px;font-size: 16px;"> <span>{{ item.more }}</span><el-icon>
                    <Right />
                  </el-icon></div>
              </div>
           
          </div>
        </div>
        <div class="research-institute-imgs" v-if="activeIndex == 2">
          <el-carousel class="research-carousel" height="300px" indicator-position="none" arrow="always"
            :autoplay="false" type="card" loop="true" initial-index="1" motion-blur="true" cardScale="1"
            ref="carouselRef">
            <el-carousel-item class="research-item"
              v-for="item in academySciencesData[lang].children[activeIndex].imgs">
              <div class="carouselItemView" @click="carouselImgClick(item)">
                <el-image class="carousel-image" :src="item.imgurl" fit="cover" lazy />
                <div class="text-overlay" >
                  {{ item.desc }}
                  <div style="bottom: 20px; padding-top: 20px;font-size: 16px;"> <span>{{ item.more }}</span><el-icon>
                      <Right />
                    </el-icon></div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
  </div>
</template>

<script  lang="ts" setup>
import { nextTick, onMounted, ref } from "vue"
import { ArrowRight } from "@element-plus/icons-vue"
import { onBeforeMount, reactive } from "vue"

import BusinessSegmentList from "./business-segment-list"
import AcademySciences_Section from "./academy-sciences-section"


import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const carouselRef = ref(null);
const router = useRouter()
const activeIndex = ref(0)
const btnClick = (item) => {
  nextTick(() => {
    activeIndex.value = item.id;    
    console.log(item.id,item.imgs);

  })
}

const academySciencesData = reactive(AcademySciences_Section)
const lang = ref(i18n.global.locale)

let businessSegmentInfo = reactive({
  key: "",
  title: "",
  desc: "",
  img: "",
  position_img: ""
})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const updateBusinessSegmentInfo = (key) => {
  const foundSegment = BusinessSegmentList.find((item) => item.key === key)
  if (foundSegment) {
    Object.assign(businessSegmentInfo, foundSegment)
  }
}

const gotoScienceAcademyDetails = (item) => {
  router.push({
    path: "/business-segment/science-academy-details",
    query: {
      key: item.key
    }
  })
}

const imgClick = (item) =>{
  
  if(item.url.trim() == ''){
    event?.stopPropagation()
    return;
  }
  router.push({
    path: item.url,
    query: {
      // key: item.key
    }
  })
}
const carouselImgClick = (item) =>{
  
  if(item.url.trim() == ''){
    return;
  }
  router.push({
    path: item.url,
    query: {
      // key: item.key
    }
  })
}
onBeforeMount(() => {
  updateBusinessSegmentInfo("AcademyOfSciences")
})

</script>

<style lang="scss" scoped>
.business-details-main {
  //background: #ffffff;
 
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  // max-width: 1920px;
  width: 100vw;
  margin: 0 auto;
  .sub-title {
    height: 100px;
    background: #f3f3f3;

    .top-breadcrumb {
      width: 1320px;
      max-width: 100%;
      margin: 0 auto;
      padding-top: 50px;

      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        font-size: 18px;

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {   
    margin: 0 auto;
    // padding: 50px 0;
    background-image: url(@/assets/business-segment/segment1bg.png);
    background-size: cover;

    .info {
      margin:0 auto;
      .title {
        
        width: 100%;
        font-weight: 600;
        font-size: 26px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 2px;
      }

      .desc {
        margin: 10px 0;
        font-size: 20px;
        color: #000000;
        line-height: 28px;
        letter-spacing: 2px;
      }
    }
    .img-container1 {
      display: flex;
      flex-direction: row;
      margin: 0 auto;
    }
    .img-view {
      width: 1046px;
      height: auto;

      .img {
        display: block;
        width: 100%;
        height: auto;
      }
    }
    .sub-tag {
      margin: 10px 0;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
    .sub-tag1 {
      margin-top: 18px;
      color: blue;
      font-size: 24px;
      font-weight: 600;
      line-height: 33px;
    }
    .sub-tag2 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 20px;
      line-height: 28px;
    }
    .imgpos {
      vertical-align: middle;
    }
  }
  // .background-container {
  //   background-image: url(@/assets/business-segment/segment1-save.png);
  //   background-size: cover;
  //   background-attachment: fixed;
  //   width: 100%;
  //   height: auto; /* 视口高度 */
  // }
  .research-institute {
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding-top: 80px;
    padding-bottom: 80px;

    display: flex;
    flex-direction: column;
    gap: 60px;

    /* 跑马灯非激活的图片透明度 */
    :deep(.el-carousel__item:not(.is-active)) {
      opacity: 0.4;
    }
    
    /** 按钮标题 */
    .research-bt-div {
      // margin:0 auto;
      text-align: center;
    }
    .btn-research {
      background-color: #f2f3f5;
      color: black;
      margin: 0 1%;
      padding: 0.5rem 1rem;
      font-size: 24px;
      border: none;
    }
    .btn-research :hover {
      background-color: #078CEC;
      color: white;
      border: 1px solid #078CEC;
      border-radius: 1rem;
      padding: 0.5rem 1rem;
      font-size: 24px;
    }
    .btn-active {
      background-color: #078CEC;
      color: white;
      margin: 0 1%;
      padding: 0.5rem 1rem;
      font-size: 24px;
    }
  
    :deep(.el-carousel__item) {
      padding: 0 8px; /* 左右各8px间距 */
    }
    
  }
  .research-institute-imgs {
    width: 100%;
  }
    .carouselItemView {
      margin: 0 auto;
      position: relative;
      display: flex;
      /* 启用flex布局 */
      justify-content:flex-start;
      /* 水平 left */
      align-items: end;
      /* 垂直 bottom */
      width: 100%;
      max-width: 800px;
    }
    .carouselItemView-si {
      position: relative;
      display: flex;
      /* 启用flex布局 */
      justify-content:flex-start;
      /* 水平 left */
      align-items: end;
      /* 垂直 bottom */
      width: 100%;
      max-width: 800px;
    }
    .text-overlay {
      position: absolute;
      z-index: 1;
      /* 确保文字在图片上方 */
      color: white;
      // text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      /* 文字阴影 */
      font-size: 24px;
      margin-left: 50px;
      margin-bottom: 50px;
    }
    
}


@media (max-width: 1320px) {
  .business-details-main .sub-title .top-breadcrumb,
  .business-details-main .content {
   width: 100%;
  }

  .business-details-main .content .img-view {
    width: 100%;
    height: auto;
  }

  .business-details-main .research-institute {
    padding-left: 20px;
    padding-right: 20px;
    gap: 100px;
  }

  .business-details-main .research-institute .energy-security {
    flex-direction: column-reverse;
    gap: 30px;
    align-items: flex-start;
  }

  .business-details-main .research-institute .energy-security .left {
    padding: 0;
  }

  .business-details-main .research-institute .energy-security .right {
    left: 0;
    width: 100%;
    height: 350px;
  }

  .business-details-main .research-institute .energy-security:hover .left {
    background: initial;

    .title,
    .desc,
    .more-btn {
      color: initial;
    }

    .line {
      border: 1px solid #979797;
    }
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 34px;
  }
}

@media (max-width: 1024px) {
  .business-details-main .content .info .title {
    font-size: 28px;
    line-height: 38px;
    min-width: 300px;
  }
  .business-details-main .content .info .desc {
    font-size: 18px;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 32px;
  }
}

@media (max-width: 820px) {
  .business-details-main .content .info {
    flex-direction: column;
    gap: 30px;
  }

  .business-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
  }

  .business-details-main .content .img-view {
    margin-top: 20px;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 30px;
  }
  .business-details-main .research-institute .energy-security .left .line {
    margin-top: 30px;
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .business-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
    line-height: 14px;
  }
}

@media (max-width: 576px) {
  .business-details-main .content .info .title {
    font-size: 24px;
    line-height: 34px;
  }

  .business-details-main .content .info .desc {
    font-size: 16px;
  }

  .business-details-main .content {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .business-details-main .research-institute .energy-security .right {
    width: 100%;
    min-width: 100%;
    height: auto;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 26px;
  }
  .business-details-main .research-institute .energy-security .left .desc {
    font-size: 16px;
  }
  .business-details-main .research-institute .energy-security .left .more-btn {
    margin-top: 30px;
  }
  .business-details-main .content .img-container1 {
    margin: 5px 10px;
    display: grid;
    grid-template-columns: 90%;
  }
  .business-details-main .content .img-container1 .img-view {
    margin: 5px 10px;
  }
  .business-details-main .content .img-container1 .sub-tag1 {
    text-align: center;
  }
  .business-details-main .content .img-container1 .sub-tag2 {
    text-align: center;
  }
}

@media (max-width: 480px) {
  :deep(.el-carousel__arrow--right) {
    top: 13%;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 24px;
  }
}

@media (max-width: 390px) {
  .business-details-main .content .info .title {
    font-size: 22px;
  }

  :deep(.el-carousel__indicators--horizontal) {
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
