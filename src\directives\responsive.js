/**
 * 响应式指令
 * 提供 v-responsive 指令，可以在模板中直接使用响应式功能
 */

import { responsiveManager, ResponsiveValueCalculator } from '@/utils/responsive'

/**
 * 响应式显示指令
 * 用法：v-responsive:show="{ mobile: false, desktop: true }"
 */
const responsiveShow = {
  mounted(el, binding) {
    const config = binding.value || {}
    const updateVisibility = () => {
      const currentBreakpoint = responsiveManager.getCurrentBreakpoint()
      const deviceType = getDeviceType(currentBreakpoint)
      
      let shouldShow = true
      
      // 检查断点特定的配置
      if (config[currentBreakpoint] !== undefined) {
        shouldShow = config[currentBreakpoint]
      }
      // 检查设备类型配置
      else if (config[deviceType] !== undefined) {
        shouldShow = config[deviceType]
      }
      // 检查默认配置
      else if (config.default !== undefined) {
        shouldShow = config.default
      }
      
      el.style.display = shouldShow ? '' : 'none'
    }
    
    // 初始更新
    updateVisibility()
    
    // 添加监听器
    const listenerId = `show-${Math.random().toString(36).substr(2, 9)}`
    responsiveManager.addListener(listenerId, updateVisibility)
    
    // 保存监听器 ID 以便清理
    el._responsiveShowListenerId = listenerId
  },
  
  updated(el, binding) {
    // 重新挂载以更新配置
    responsiveShow.unmounted(el)
    responsiveShow.mounted(el, binding)
  },
  
  unmounted(el) {
    if (el._responsiveShowListenerId) {
      responsiveManager.removeListener(el._responsiveShowListenerId)
      delete el._responsiveShowListenerId
    }
  }
}

/**
 * 响应式类名指令
 * 用法：v-responsive:class="{ mobile: 'mobile-class', desktop: 'desktop-class' }"
 */
const responsiveClass = {
  mounted(el, binding) {
    const config = binding.value || {}
    let currentClasses = []
    
    const updateClasses = () => {
      const currentBreakpoint = responsiveManager.getCurrentBreakpoint()
      const deviceType = getDeviceType(currentBreakpoint)
      
      // 移除之前的类名
      currentClasses.forEach(className => {
        el.classList.remove(className)
      })
      currentClasses = []
      
      // 添加新的类名
      let classesToAdd = []
      
      // 检查断点特定的配置
      if (config[currentBreakpoint]) {
        classesToAdd = Array.isArray(config[currentBreakpoint]) 
          ? config[currentBreakpoint] 
          : [config[currentBreakpoint]]
      }
      // 检查设备类型配置
      else if (config[deviceType]) {
        classesToAdd = Array.isArray(config[deviceType]) 
          ? config[deviceType] 
          : [config[deviceType]]
      }
      // 检查默认配置
      else if (config.default) {
        classesToAdd = Array.isArray(config.default) 
          ? config.default 
          : [config.default]
      }
      
      classesToAdd.forEach(className => {
        if (className) {
          el.classList.add(className)
          currentClasses.push(className)
        }
      })
    }
    
    // 初始更新
    updateClasses()
    
    // 添加监听器
    const listenerId = `class-${Math.random().toString(36).substr(2, 9)}`
    responsiveManager.addListener(listenerId, updateClasses)
    
    // 保存监听器 ID 和当前类名以便清理
    el._responsiveClassListenerId = listenerId
    el._responsiveCurrentClasses = currentClasses
  },
  
  updated(el, binding) {
    // 重新挂载以更新配置
    responsiveClass.unmounted(el)
    responsiveClass.mounted(el, binding)
  },
  
  unmounted(el) {
    if (el._responsiveClassListenerId) {
      responsiveManager.removeListener(el._responsiveClassListenerId)
      delete el._responsiveClassListenerId
    }
    
    // 清理类名
    if (el._responsiveCurrentClasses) {
      el._responsiveCurrentClasses.forEach(className => {
        el.classList.remove(className)
      })
      delete el._responsiveCurrentClasses
    }
  }
}

/**
 * 响应式样式指令
 * 用法：v-responsive:style="{ mobile: { fontSize: '14px' }, desktop: { fontSize: '18px' } }"
 */
const responsiveStyle = {
  mounted(el, binding) {
    const config = binding.value || {}
    let appliedStyles = {}
    
    const updateStyles = () => {
      const currentBreakpoint = responsiveManager.getCurrentBreakpoint()
      const deviceType = getDeviceType(currentBreakpoint)
      
      // 移除之前的样式
      Object.keys(appliedStyles).forEach(property => {
        el.style[property] = ''
      })
      appliedStyles = {}
      
      // 应用新的样式
      let stylesToApply = {}
      
      // 检查断点特定的配置
      if (config[currentBreakpoint]) {
        stylesToApply = config[currentBreakpoint]
      }
      // 检查设备类型配置
      else if (config[deviceType]) {
        stylesToApply = config[deviceType]
      }
      // 检查默认配置
      else if (config.default) {
        stylesToApply = config.default
      }
      
      Object.entries(stylesToApply).forEach(([property, value]) => {
        if (value !== undefined && value !== null) {
          el.style[property] = value
          appliedStyles[property] = value
        }
      })
    }
    
    // 初始更新
    updateStyles()
    
    // 添加监听器
    const listenerId = `style-${Math.random().toString(36).substr(2, 9)}`
    responsiveManager.addListener(listenerId, updateStyles)
    
    // 保存监听器 ID 和应用的样式以便清理
    el._responsiveStyleListenerId = listenerId
    el._responsiveAppliedStyles = appliedStyles
  },
  
  updated(el, binding) {
    // 重新挂载以更新配置
    responsiveStyle.unmounted(el)
    responsiveStyle.mounted(el, binding)
  },
  
  unmounted(el) {
    if (el._responsiveStyleListenerId) {
      responsiveManager.removeListener(el._responsiveStyleListenerId)
      delete el._responsiveStyleListenerId
    }
    
    // 清理样式
    if (el._responsiveAppliedStyles) {
      Object.keys(el._responsiveAppliedStyles).forEach(property => {
        el.style[property] = ''
      })
      delete el._responsiveAppliedStyles
    }
  }
}

/**
 * 响应式属性指令
 * 用法：v-responsive:attr="{ mobile: { 'data-size': 'small' }, desktop: { 'data-size': 'large' } }"
 */
const responsiveAttr = {
  mounted(el, binding) {
    const config = binding.value || {}
    let appliedAttrs = {}
    
    const updateAttrs = () => {
      const currentBreakpoint = responsiveManager.getCurrentBreakpoint()
      const deviceType = getDeviceType(currentBreakpoint)
      
      // 移除之前的属性
      Object.keys(appliedAttrs).forEach(attrName => {
        el.removeAttribute(attrName)
      })
      appliedAttrs = {}
      
      // 应用新的属性
      let attrsToApply = {}
      
      // 检查断点特定的配置
      if (config[currentBreakpoint]) {
        attrsToApply = config[currentBreakpoint]
      }
      // 检查设备类型配置
      else if (config[deviceType]) {
        attrsToApply = config[deviceType]
      }
      // 检查默认配置
      else if (config.default) {
        attrsToApply = config.default
      }
      
      Object.entries(attrsToApply).forEach(([attrName, value]) => {
        if (value !== undefined && value !== null) {
          el.setAttribute(attrName, value)
          appliedAttrs[attrName] = value
        }
      })
    }
    
    // 初始更新
    updateAttrs()
    
    // 添加监听器
    const listenerId = `attr-${Math.random().toString(36).substr(2, 9)}`
    responsiveManager.addListener(listenerId, updateAttrs)
    
    // 保存监听器 ID 和应用的属性以便清理
    el._responsiveAttrListenerId = listenerId
    el._responsiveAppliedAttrs = appliedAttrs
  },
  
  updated(el, binding) {
    // 重新挂载以更新配置
    responsiveAttr.unmounted(el)
    responsiveAttr.mounted(el, binding)
  },
  
  unmounted(el) {
    if (el._responsiveAttrListenerId) {
      responsiveManager.removeListener(el._responsiveAttrListenerId)
      delete el._responsiveAttrListenerId
    }
    
    // 清理属性
    if (el._responsiveAppliedAttrs) {
      Object.keys(el._responsiveAppliedAttrs).forEach(attrName => {
        el.removeAttribute(attrName)
      })
      delete el._responsiveAppliedAttrs
    }
  }
}

/**
 * 获取设备类型
 * @param {string} breakpoint - 断点
 * @returns {string} 设备类型
 */
function getDeviceType(breakpoint) {
  if (['xs', 'sm'].includes(breakpoint)) return 'mobile'
  if (['md', 'lg'].includes(breakpoint)) return 'tablet'
  return 'desktop'
}

/**
 * 响应式指令主入口
 */
const responsive = {
  mounted(el, binding) {
    const arg = binding.arg || 'show'
    
    switch (arg) {
      case 'show':
        responsiveShow.mounted(el, binding)
        break
      case 'class':
        responsiveClass.mounted(el, binding)
        break
      case 'style':
        responsiveStyle.mounted(el, binding)
        break
      case 'attr':
        responsiveAttr.mounted(el, binding)
        break
      default:
        console.warn(`Unknown responsive directive argument: ${arg}`)
    }
  },
  
  updated(el, binding) {
    const arg = binding.arg || 'show'
    
    switch (arg) {
      case 'show':
        responsiveShow.updated(el, binding)
        break
      case 'class':
        responsiveClass.updated(el, binding)
        break
      case 'style':
        responsiveStyle.updated(el, binding)
        break
      case 'attr':
        responsiveAttr.updated(el, binding)
        break
    }
  },
  
  unmounted(el, binding) {
    const arg = binding.arg || 'show'
    
    switch (arg) {
      case 'show':
        responsiveShow.unmounted(el)
        break
      case 'class':
        responsiveClass.unmounted(el)
        break
      case 'style':
        responsiveStyle.unmounted(el)
        break
      case 'attr':
        responsiveAttr.unmounted(el)
        break
    }
  }
}

export default responsive
