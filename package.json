{"name": "website", "version": "4.4.0", "description": "官网", "author": {"name": "LeoCui & Melody"}, "repository": {}, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@headlessui/vue": "^1.7.19", "@heroicons/vue": "^2.1.3", "axios": "1.6.8", "dayjs": "1.11.10", "echarts": "^5.5.0", "element-plus": "2.7.8", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "mitt": "3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "6.2.1", "pinia": "2.1.7", "screenfull": "6.0.2", "v3-drag-zoom": "^1.1.20", "vite-plugin-compression": "^0.5.1", "vue": "3.4.21", "vue-i18n": "^10.0.0-alpha.4", "vue-router": "4.3.0", "xe-utils": "3.5.22"}, "devDependencies": {"@rollup/pluginutils": "^5.1.0", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "@vue/eslint-config-prettier": "9.0.0", "@vue/test-utils": "2.4.5", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "8.57.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-vue": "9.23.0", "husky": "9.0.11", "jsdom": "24.0.0", "lint-staged": "15.2.2", "postcss": "^8.4.38", "prettier": "3.2.5", "rollup-plugin-terser": "^7.0.2", "sass": "1.72.0", "tailwindcss": "^3.4.3", "vite": "5.2.6", "vite-plugin-svg-icons": "2.0.1", "vite-svg-loader": "5.1.0", "vitest": "1.4.0", "vue-eslint-parser": "9.4.2", "vue-tsc": "2.0.7"}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,less,scss,html,md}": ["prettier --write"], "package.json": ["prettier --write"]}, "keywords": ["vue", "vue3", "admin", "vue-admin", "vue3-admin", "vite", "vite-admin", "element-plus", "element-plus-admin", "ts"], "license": ""}