// vite.config.js
import { loadEnv } from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/vite/dist/node/index.js";
import path, { resolve } from "path";
import vue from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { createSvgIconsPlugin } from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import svgLoader from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/vite-svg-loader/index.js";
import { terser } from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/rollup-plugin-terser/rollup-plugin-terser.mjs";
import viteCompression from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/vite-plugin-compression/dist/index.mjs";
import { createFilter } from "file:///D:/git/git%E9%A1%B9%E7%9B%AE%E7%BE%A4%E7%BB%84/sinofortune-website/node_modules/@rollup/pluginutils/dist/es/index.js";
var __vite_injected_original_dirname = "D:\\git\\git\u9879\u76EE\u7FA4\u7EC4\\sinofortune-website";
var vite_config_default = ({ mode }) => {
  const viteEnv = loadEnv(mode, process.cwd());
  const { VITE_PUBLIC_PATH } = viteEnv;
  return {
    /** 打包时根据实际情况修改 base */
    base: VITE_PUBLIC_PATH,
    resolve: {
      alias: {
        /** @ 符号指向 src 目录 */
        "@": resolve(__vite_injected_original_dirname, "./src")
      }
    },
    server: {
      /** 设置 host: true 才可以使用 Network 的形式，以 IP 访问项目 */
      host: true,
      // host: "0.0.0.0"
      /** 端口号 */
      port: 3005,
      /** 是否自动打开浏览器 */
      open: false,
      /** 跨域设置允许 */
      cors: true,
      /** 端口被占用时，是否直接退出 */
      strictPort: false,
      /** 接口代理 */
      proxy: {
        // "/api": {
        //   target: "http://localhost:3050",
        //   ws: true,
        //   /** 是否允许跨域 */
        //   changeOrigin: true
        // }
      },
      /** 预热常用文件，提高初始页面加载速度 */
      warmup: {
        clientFiles: ["./src/layouts/**/*.vue"]
      }
    },
    build: {
      /** 单个 chunk 文件的大小超过 2048KB 时发出警告 */
      chunkSizeWarningLimit: 2048,
      /** 禁用 gzip 压缩大小报告 */
      reportCompressedSize: false,
      /** 打包后静态资源目录 */
      assetsDir: "static",
      rollupOptions: {
        output: {
          /**
           * 分块策略
           * 1. 注意这些包名必须存在，否则打包会报错
           * 2. 如果你不想自定义 chunk 分割策略，可以直接移除这段配置
           */
          manualChunks: {
            vue: ["vue", "vue-router", "pinia"],
            element: ["element-plus", "@element-plus/icons-vue"],
            vxe: ["xe-utils"]
          }
        },
        plugins: [
          // 压缩代码 先 esbuild 快速压缩 再 terser 压缩率高
          terser({
            compress: {
              drop_console: true,
              // 删除 console.log
              drop_debugger: true,
              // 删除 debugger
              dead_code: true,
              // 删除无用的代码
              unused: true
              // 删除未使用的变量或函数
            },
            format: {
              comments: false
              // 删除所有注释
            },
            toplevel: true,
            // 删除无用代码
            mangle: true
            // 短化变量名
          })
        ]
      }
    },
    // esbuild 压缩速度块 但是 terser 压缩率高
    /** 混淆器 */
    esbuild: mode === "development" ? void 0 : {
      /** 打包时移除 console.log */
      pure: ["console.log"],
      /** 打包时移除 debugger */
      drop: ["debugger"],
      /** 打包时移除所有注释 */
      legalComments: "none"
    },
    /** Vite 插件 */
    plugins: [
      {
        name: "skip-video-files",
        generateBundle(options, bundle) {
          const filter = createFilter(/\.(mp4|webm|ogg|mov)$/i);
          for (const name in bundle) {
            if (filter(name)) {
              delete bundle[name];
            }
          }
        }
      },
      viteCompression({
        filter: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
        // 需要压缩的文件
        threshold: 600,
        // 文件容量大于这个值进行压缩
        algorithm: "gzip",
        // 压缩方式
        ext: "gz",
        // 后缀名
        level: 9,
        // 设置压缩级别为6
        deleteOriginFile: false
        // 压缩后是否删除压缩源文件
      }),
      vue(),
      vueJsx(),
      /** 将 SVG 静态图转化为 Vue 组件 */
      svgLoader({ defaultImport: "url" }),
      /** SVG */
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/icons/svg")],
        symbolId: "icon-[dir]-[name]"
      })
    ],
    /** Vitest 单元测试配置：https://cn.vitest.dev/config */
    test: {
      include: ["tests/**/*.test.ts"],
      environment: "jsdom"
    }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
