<!--
 * 响应式容器组件
 * 提供统一的响应式容器布局，支持不同设备的自适应显示
 -->
<template>
  <div 
    :class="containerClasses" 
    :style="containerStyles"
    class="responsive-container"
  >
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useResponsiveContainer, useResponsiveClass } from '@/hooks/useResponsive'

const props = defineProps({
  // 是否启用最大宽度限制
  maxWidth: {
    type: Boolean,
    default: true
  },
  // 是否启用响应式内边距
  padding: {
    type: Boolean,
    default: true
  },
  // 自定义类名映射
  customClasses: {
    type: Object,
    default: () => ({})
  },
  // 是否居中对齐
  center: {
    type: Boolean,
    default: true
  },
  // 容器类型
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'fluid', 'narrow', 'wide'].includes(value)
  }
})

// 使用响应式容器 hook
const { containerStyle } = useResponsiveContainer()

// 响应式类名
const responsiveClasses = useResponsiveClass({
  mobile: 'container-mobile',
  tablet: 'container-tablet',
  desktop: 'container-desktop',
  ...props.customClasses
})

// 计算容器类名
const containerClasses = computed(() => {
  const classes = [responsiveClasses.value]
  
  if (props.type !== 'default') {
    classes.push(`container-${props.type}`)
  }
  
  if (props.center) {
    classes.push('container-center')
  }
  
  return classes
})

// 计算容器样式
const containerStyles = computed(() => {
  const styles = {}
  
  if (props.maxWidth) {
    styles.maxWidth = containerStyle.value.maxWidth
  }
  
  if (props.padding) {
    styles.paddingLeft = containerStyle.value.paddingLeft
    styles.paddingRight = containerStyle.value.paddingRight
  }
  
  if (props.center) {
    styles.marginLeft = 'auto'
    styles.marginRight = 'auto'
  }
  
  return styles
})
</script>

<style lang="scss" scoped>
.responsive-container {
  width: 100%;
  
  // 容器类型样式
  &.container-fluid {
    max-width: none !important;
  }
  
  &.container-narrow {
    @include respond-to(lg) {
      max-width: 800px;
    }
  }
  
  &.container-wide {
    @include respond-to(xl) {
      max-width: 1400px;
    }
    
    @include respond-to(xxl) {
      max-width: 1600px;
    }
  }
  
  &.container-center {
    margin-left: auto;
    margin-right: auto;
  }
  
  // 设备特定样式
  &.container-mobile {
    @include mobile-only {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
  
  &.container-tablet {
    @include tablet-only {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  &.container-desktop {
    @include desktop-only {
      padding-left: 4rem;
      padding-right: 4rem;
    }
  }
}
</style>
