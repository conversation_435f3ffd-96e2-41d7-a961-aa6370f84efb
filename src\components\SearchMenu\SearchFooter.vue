<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-04-06 19:23:47
-->
<template>
  <div class="search-footer">
    <template v-if="!isMobile">
      <span class="search-footer-item">
        <SvgIcon name="keyboard-enter" />
        <span>确认</span>
      </span>
      <span class="search-footer-item">
        <SvgIcon name="keyboard-up" />
        <SvgIcon name="keyboard-down" />
        <span>切换</span>
      </span>
      <span class="search-footer-item">
        <SvgIcon name="keyboard-esc" />
        <span>关闭</span>
      </span>
    </template>
    <span class="search-footer-total">共 {{ props.total }} 项</span>
  </div>
</template>

<script setup>
import { useDevice } from "@/hooks/useDevice"

const props = defineProps({
  total: {
    type: Number,
    default: 0
  }
})

const { isMobile } = useDevice()
</script>

<style lang="scss" scoped>
.search-footer {
  display: flex;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  &-item {
    display: flex;
    align-items: center;
    margin-right: 12px;
    .svg-icon {
      margin-right: 5px;
      padding: 2px;
      font-size: 20px;
      background-color: var(--el-fill-color);
    }
  }
  &-total {
    margin: 0 0 0 auto;
  }
}
</style>
