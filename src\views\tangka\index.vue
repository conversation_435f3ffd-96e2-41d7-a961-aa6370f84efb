<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-26 18:20:06
-->

<template>
  <div class="tangka-main">
    <div class="top-desc">
      <div class="view">
        <!-- navigate -->
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart1' }" @click="gotoDomView('tangkaPart1')">
          {{ $t("tangka.generalView") }}
        </div>
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart2' }" @click="gotoDomView('tangkaPart2')">
          {{ $t("tangka.teacherInfo") }}
        </div>
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart3' }" @click="gotoDomView('tangkaPart3')">
          {{ $t("tangka.honor") }}
        </div>
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart4' }" @click="gotoDomView('tangkaPart4')">
          {{ $t("tangka.achievements") }}
        </div>
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart5' }" @click="gotoDomView('tangkaPart5')">
          {{ $t("tangka.liftTree") }}
        </div>
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart6' }" @click="gotoDomView('tangkaPart6')">
          {{ $t("tangka.museum") }}
        </div>
        <div class="item" :class="{ 'item-at': activeItem == 'tangkaPart7' }" @click="gotoDomView('tangkaPart7')">
          {{ $t("tangka.famousWorks") }}
        </div>
      </div>

      <!-- <div class="view">
        <div class="item">3D全景</div>
      </div> -->
    </div>

    <div class="all-info-view">
      <div class="banner-view" id="tangkaPart1">
        <div class="banner-main">
          <el-carousel class="banner-carousel" height="auto">
            <el-carousel-item :style="`height: ${itemStyleHeight}px`" v-for="url in bannerListUrl" :key="url">
              <div class="banner-main-carousel-item">
                <el-image @load="loadImage" class="carousel-image" :src="url" fit="cover" />
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <div id="tangkaPart1_1">
        <div class="preface">
          <h1 class="title">{{ $t("Preface.title") }}</h1>
          <div class="content">
            <p>{{ $t("Preface.content1") }}</p>
            <p>{{ $t("Preface.content2") }}</p>
          </div>
        </div>
      </div>
      <div class="character-info" id="tangkaPart2">
        <div class="character-main">
          <div class="desc-view user-info">
            <div class="desc">
              <div class="img-view">
                <div class="img">
                  <el-image :src="teacher" fit="cover" />
                </div>
              </div>
              <div class="txt-view">
                <div class="txt">
                  <h3>{{ $t("teacherInfo.name") }}</h3>
                  <p>{{ $t("teacherInfo.birthday") }}</p>
                  <p>{{ $t("teacherInfo.oneInfo") }}</p>
                  <p>{{ $t("teacherInfo.twoInfo") }}</p>
                  <p>{{ $t("teacherInfo.threeInfo") }}</p>
                  <p>{{ $t("teacherInfo.teacherInfo_d5") }}</p>
                  <p>{{ $t("teacherInfo.teacherInfo_d6") }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="character-info" id="tangkaPart3">
        <div class="character-main">
          <div class="desc-view desc-view-i2">
            <div class="desc">
              <div class="txt-view">
                <div class="txt">
                  <h3>{{ $t("reputation.title") }}</h3>
                  <p>{{ $t("reputation.oneInfo") }}</p>
                  <p>{{ $t("reputation.twoInfo") }}</p>
                  <p>{{ $t("reputation.threeInfo") }}</p>
                  <p>{{ $t("reputation.reputation_d10") }}</p>
                  <p>{{ $t("reputation.fourInfo") }}</p>
                  <p>{{ $t("reputation.fiveInfo") }}</p>
                  <p>{{ $t("reputation.sixInfo") }}</p>
                  <p>{{ $t("reputation.sevenInfo") }}</p>
                  <p>{{ $t("reputation.reputation_d11") }}</p>
                  <p>{{ $t("reputation.reputation_d12") }}</p>
                  <p>{{ $t("reputation.reputation_d13") }}</p>
                  <p>{{ $t("reputation.reputation_d14") }}</p>
                  <p>{{ $t("reputation.reputation_d15") }}</p>
                  <p>{{ $t("reputation.reputation_d16") }}</p>
                  <p>{{ $t("reputation.reputation_d17") }}</p>
                  <p>{{ $t("reputation.reputation_d18") }}</p>
                  <p>{{ $t("reputation.reputation_d19") }}</p>
                </div>
              </div>

              <div class="img-view img-view-i2">
                <div class="tangka-certificate">
                  <el-carousel class="certificate-carousel" height="auto" indicator-position="none" arrow="always"
                    :autoplay="false">
                    <el-carousel-item class="certificate-item">
                      <div>
                        <div class="certificate-img" >
                          <el-image :src="certificateImg1" fit="cover" class="certificate-img-i"/>
                        </div>
                        <div class="certificate-img-bm">
                          <el-image :src="certificateImgbg" fit="cover"/>
                        </div>
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="certificate-item">
                      <div>
                        <div class="certificate-img" >
                          <el-image :src="certificateImg2" fit="cover" class="certificate-img-i"/>
                        </div>
                        <div class="certificate-img-bm">
                          <el-image :src="certificateImgbg" fit="cover" />
                        </div>
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="certificate-item">
                      <div>
                        <div class="certificate-img">
                          <el-image :src="certificateImg3" fit="cover" class="certificate-img-i" />
                        </div>
                        <div class="certificate-img-bm">
                          <el-image :src="certificateImgbg" fit="cover"/>
                        </div>
                      </div>
                    </el-carousel-item>
                  </el-carousel>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="achievement-info" id="tangkaPart4">
        <div class="achievement-info-main">
          <div class="title">{{ achievement[lang].title }}</div>          
          <div class="achievement-zone">
            <div class="achievement-view">
              <div v-for="yds in achievements">
                <div v-for="yd in yds.ydata" class="item">                 
                    <div class="time">{{ yd.mon }}</div>
                    <div class="desc">
                      <div v-for="md in yd.mdata">
                          {{ md.content }}
                      </div>
                    </div>                  
                </div>
              </div>
            </div>
            <div class="achievement-right-wrap">
              <div class="wrap-box">
                <div class="button-wrap">
                  <div class="up" @click="onclick_ach_up"></div>
                </div>
                <div class="showYear">
                  <div @click="showEvent(it)" v-for="it in showYearList" :class="[activeYear === it?'dateTabItemAcitve':'dateTabItem']">{{it}}</div>                  
                </div>
                <div class="button-wrap">
                  <div class="botton-box down"  @click="onclick_ach_down"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Life Tree -->
      <div class="character-info" id="tangkaPart5">
        <div class="character-main">
          <div class="desc-view desc-view-i2">
            <div class="desc">
              <div class="txt-view-lifetree">
                <div class="txt">
                  <h3><span>{{ $t("liftTree.title1") }}</span>{{ $t("liftTree.title2") }}</h3>
                  <p>{{ $t("liftTree.content1") }}</p>
                  <p>{{ $t("liftTree.content2") }}</p>
                  <p>{{ $t("liftTree.content3") }}</p>
                </div>
                <div class="img-view">
                  <div class="img">
                    <div class="img"><el-image :src="liftTreeImg" fit="cover" /></div>
                    <p>{{ $t("liftTree.title1") }}</p>
                  </div>
                </div>
              </div>

              <div class="img-view img-view-i2">
                <div class="tangka-certificate">
                  <el-carousel class="lifetree-carousel" height="auto" indicator-position="none" arrow="always"
                    :autoplay="false" type="card" loop="true" initial-index="1" cardScale="1" motion-blur="true">
                    <el-carousel-item class="lifetree-item">
                      <div>
                        <div class="lifetree-img">
                          <el-image :src="liftTreeImg1" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="lifetree-item">
                      <div>
                        <div class="lifetree-img">
                          <el-image :src="liftTreeImg2" fit="cover"  class="img"/>
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="lifetree-item">
                      <div>
                        <div class="lifetree-img">
                          <el-image :src="liftTreeImg3" fit="cover"  class="img"/>
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="lifetree-item">
                      <div>
                        <div class="lifetree-img">
                          <el-image :src="liftTreeImg4" fit="cover"  class="img"/>
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="lifetree-item">
                      <div>
                        <div class="lifetree-img">
                          <el-image :src="liftTreeImg5" fit="cover"  class="img"/>
                        </div>                        
                      </div>
                    </el-carousel-item>
                  </el-carousel>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- museum -->
      <div class="character-info" id="tangkaPart6">
        <div class="character-main">
          <div class="desc-view desc-view-i2">
            <div class="desc">
              <div class="txt-view-lifetree">
                <div class="txt">
                  <h1><span>{{ $t("museum.title1") }}</span>{{ $t("museum.title2") }}</h1>
                  <p>{{ $t("museum.content1") }}</p>
                  <p>{{ $t("museum.content2") }}</p>
                  <p>{{ $t("museum.content3") }}</p>
                </div>                
              </div>

              <div class="img-view img-view-i2">
                <div class="tangka-certificate">
                  <el-carousel class="museum-carousel" height="auto" indicator-position="none" arrow="always"
                    :autoplay="false" type="" loop="true" initial-index="1" motion-blur="true">
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg1" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg2" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg3" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg4" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg5" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg6" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg7" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg8" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg9" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    <el-carousel-item class="museum-item">
                      <div>
                        <div class="museum-img">
                          <el-image :src="museumImg10" fit="cover" class="img" />
                        </div>                        
                      </div>
                    </el-carousel-item>
                    
                  </el-carousel>
                </div>
              </div>
              
            </div>
          </div>
        </div>
      </div>
      <div id="tangkaPart7">
        <TangkaList />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, reactive, onBeforeUpdate } from "vue"

import achievement from "./achievement-list"
import worksList from "./works-list"
import familyList from "./family-list"
import bannerImg1 from "@/assets/tangka/bannerImg1.jpg"
import bannerImg2 from "@/assets/tangka/bannerImg2.jpg"
import bannerImg3 from "@/assets/tangka/bannerImg3.jpg"
import teacher from "@/assets/tangka/great-master.jpg"

import certificateImgbg from "@/assets/tangka/cert_bg.png"
import certificateImg1 from "@/assets/tangka/certificateImg1all.png"
import certificateImg2 from "@/assets/tangka/certificateImg2all.png"
import certificateImg3 from "@/assets/tangka/certificateImg3all.png"

import liftTreeImg from "@/assets/tangka/lifetreeimgs/lifetree.png"
import liftTreeImg1 from "@/assets/tangka/lifetreeimgs/1.jpg"
import liftTreeImg2 from "@/assets/tangka/lifetreeimgs/2.jpg"
import liftTreeImg3 from "@/assets/tangka/lifetreeimgs/3.png"
import liftTreeImg4 from "@/assets/tangka/lifetreeimgs/4.jpg"
import liftTreeImg5 from "@/assets/tangka/lifetreeimgs/5.png"

import museumImg1 from "@/assets/tangka/museumimgs/1.png"
import museumImg2 from "@/assets/tangka/museumimgs/2.png"
import museumImg3 from "@/assets/tangka/museumimgs/3.png"
import museumImg4 from "@/assets/tangka/museumimgs/4.png"
import museumImg5 from "@/assets/tangka/museumimgs/5.png"
import museumImg6 from "@/assets/tangka/museumimgs/6.png"
import museumImg7 from "@/assets/tangka/museumimgs/7.png"
import museumImg8 from "@/assets/tangka/museumimgs/8.png"
import museumImg9 from "@/assets/tangka/museumimgs/9.png"
import museumImg10 from "@/assets/tangka/museumimgs/10.png"

import { i18n } from "@/i18n"

import TangkaList from "./tangka-list.vue"

import { watch } from "vue"
import { useDevice } from "@/hooks/useDevice"
import { useRouter } from "vue-router"

const itemStyleHeight = ref(0)
const router = useRouter()
const { isMobile } = useDevice()

const _worksList = [...worksList]

const getTangkaWorksPage = (pageNum, pageSize) => {
  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  return worksList.slice(start, end)
}

const currentPage = ref(1)
const worksListData = ref(getTangkaWorksPage(currentPage.value, 4))

const bannerListUrl = reactive([bannerImg1, bannerImg2, bannerImg3])

const activeItem = ref("tangkaPart1")

const gotoDomView = (id) => {
  activeItem.value = id

  nextTick(() => {
    const dom = document.getElementById(id)
    dom?.scrollIntoView({
      behavior: "smooth"
    })
  })

  localStorage.setItem("tangkaPart", id)
}

const prevPage = () => {
  currentPage.value--
  if (currentPage.value < 1) {
    currentPage.value = 1
  }

  worksListData.value = getTangkaWorksPage(currentPage.value, 4)
}

const nextPage = () => {
  currentPage.value++
  if (currentPage.value > Math.ceil(worksList.length / 4)) {
    currentPage.value = Math.ceil(worksList.length / 4)
  }

  worksListData.value = getTangkaWorksPage(currentPage.value, 4)
}

const showTangkaList = () => {
  activeItem.value = "tangkaPart6"
  localStorage.setItem("tangkaPart", "tangkaPart6")

  nextTick(() => {
    document.documentElement.scrollTop = 0
  })
}

const lang = ref(i18n.global.locale)
watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

//console.log('lang:',lang.value);
const activeYear = ref('2018')
const achievements = ref([])
let listAch= achievement[lang.value].data.filter(f=>f.yr === activeYear.value);
achievements.value =listAch;
console.log('achievements:',listAch);
const showYearList = ref(['2018','2017','2016'])

watch(activeYear,
  (newVal)=>{
    let l = achievement[lang.value].data.filter(f=> f.yr === activeYear.value);
    achievements.value =l;
  }
)

const topList = familyList.filter((_, index) => index % 2 === 0)

const bottomList = familyList.filter((_, index) => index % 2 !== 0)

const gotoRelevanceDetails = (id) => {
  router.push({ name: "TangkaDetails", query: { id } })
}

const refreshBusinessList = () => {
  const businessCarouselItems = document.querySelectorAll(".banner-main-carousel-item")
  let maxHeight = 0
  businessCarouselItems.forEach((item) => {
    maxHeight = Math.max(maxHeight, item.clientHeight)
  })

  itemStyleHeight.value = maxHeight

  loadImgNum.value = 0
}

const loadImgNum = ref(0)
const loadImage = () => {
  loadImgNum.value++

  if (loadImgNum.value === bannerListUrl.length) {
    refreshBusinessList()
  }
}

const loadingMore = () => {
  currentPage.value++
  worksListData.value.push(...getTangkaWorksPage(currentPage.value, 4))
}

const showEvent = (year) =>{
  console.log(year);
  activeYear.value = year;
  // console.log(achievementList[lang.value]);
  let l = achievement[lang.value].data.filter(f=> f.yr === year);
  achievements.value =l;
  console.log(achievements.value);
}
const onclick_ach_up = () => {
  console.log('up before', activeYear.value);
  switch(activeYear.value){
    case '2018':
      break;
    case '2017':
      activeYear.value = '2018';
      break;
    case '2016':
      activeYear.value = '2017';
      break;
    case '~2015':
      activeYear.value = '2016';
      break;  
  }
  console.log('up after', activeYear.value);
  if(!showYearList.value.find(f=>f=== activeYear.value)){
    showYearList.value= [activeYear.value,...showYearList.value.slice(-3,-1)];
  }
}
const onclick_ach_down = () => {
  console.log('down before',activeYear.value);
  switch(activeYear.value){
    case '2018':
      activeYear.value = '2017';
      break;
    case '2017':
      activeYear.value = '2016';
      break;
    case '2016':
      activeYear.value = '~2015';
      break;
    case '~2015':      
      break;  
  }
  console.log('down after',activeYear.value);
  if(!showYearList.value.find(f=>f=== activeYear.value)){
    showYearList.value= [...showYearList.value.slice(1,3),activeYear.value];
  }
}
onMounted(() => {
  
  const part = localStorage.getItem("tangkaPart")
  if (part) {
    gotoDomView(part)
  }

  window.addEventListener("resize", () => {
    refreshBusinessList()

    const screenWidth = window.innerWidth
    if (screenWidth > 820 && worksListData.value.length > 4) {
      worksListData.value = worksList.slice(0, 4)
    }
  })
})

onBeforeUnmount(() => {
  
  const part = localStorage.getItem("tangkaPart")
  if (part) {
    localStorage.removeItem("tangkaPart")
  }
})

onBeforeUpdate(() => {
  window.removeEventListener("resize", () => {})
})

const timeLineArray = [];
</script>

<style lang="scss" scoped>
@import 'tangka.scss';
</style>