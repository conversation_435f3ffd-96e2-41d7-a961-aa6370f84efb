<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-15 20:10:55
-->

<template>
  <div class="home">
    <HomeCarousel />
    <ThreeCore titleType="home" />
    <IndustrialBase />
    <EnergyValley />
    <NewsContent />
  </div>
</template>

<script setup>
import HomeCarousel from "./components/HomeCarousel.vue"
import ThreeCore from "./components/ThreeCore.vue"
import IndustrialBase from "./components/IndustrialBase.vue"
import EnergyValley from "./components/EnergyValley.vue"

import NewsContent from "./components/NewsContent.vue"
</script>

<style lang="scss" scoped>
.home {
  background: #ffffff;
}
</style>
