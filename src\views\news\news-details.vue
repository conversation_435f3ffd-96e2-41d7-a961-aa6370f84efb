<!--
 * @Author: <PERSON>
 * @Date: 2024-05-15 20:10:53
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-10-18 09:52:23
-->
<template>
  <div class="news-details">
    <div class="breadcrumb">
      <div class="econtainer">
        <el-breadcrumb :separator-icon="ArrowRight" class="item-color">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("newsDetails.title1") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/news' }">{{ $t("newsDetails.title2") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("newsDetails.title3") }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <div class="content">
      <div class="news-list">
        <div class="item" v-if="!loading">
          <div class="title">{{ getNewTitle() }}</div>
          <div class="time">{{ getTime() }}</div>
          <div class="desc" v-html="getNewContent()"></div>
          <div class="img-box" v-for="(item, index) in imgList" :key="index">
            <el-image class="img" :src="item" alt="" fit="cover" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus"
import { ref } from "vue"

import { useRouter } from "vue-router"
import { i18n } from "@/i18n"
import { watch } from "vue"
import { ArrowRight } from "@element-plus/icons-vue"

import dayjs from "dayjs"

import { newDetail } from "@/api/new/index.js"

const router = useRouter()
const newId = ref(0)
newId.value = router.currentRoute.value.query.id
const lang = ref(i18n.global.locale)

const loading = ref(true)

const imgList = ref([])

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

let newInfo = ref({})

const getNewDetail = () => {
  const params = {
    id: newId.value
  }
  newDetail(params).then((res) => {
    if (res.code === 200 && res.data) {
      loading.value = false

      newInfo.value = res.data

      if (newInfo.value?.newContentImg) {
        imgList.value = JSON.parse(newInfo.value?.newContentImg)
      }
    } else {
      ElMessage.warning("没有找到新闻")
      setTimeout(() => {
        router.push({ path: "/news" })
      }, 1000)
    }
  })
}
getNewDetail()

const getNewTitle = () => {
  const selectMap = {
    zh: newInfo.value.titleZh,
    zh_hk: newInfo.value.titleHk,
    en: newInfo.value.titleEn
  }
  return selectMap[lang.value]
}

const getTime = () => {
  return dayjs(newInfo.value.newTime).format("YYYY-MM-DD")
}

const getNewContent = () => {
  const selectMap = {
    zh: `<p>${newInfo.value.contentZh.replace(/\n+/g, "\n").replace(/\n/g, "</p><p>")}</p>`,
    zh_hk: `<p>${newInfo.value.contentHk.replace(/\n+/g, "\n").replace(/\n/g, "</p><p>")}</p>`,
    en: `<p>${newInfo.value.contentEn.replace(/\n+/g, "\n").replace(/\n/g, "</p><p>")}</p>`
  }

  return selectMap[lang.value]
}
</script>

<style lang="scss" scoped>
.news-details {
  padding-bottom: 150px;

  .breadcrumb {
    background-color: #f3f3f3;
  }

  .econtainer {
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;

    :deep(.el-breadcrumb) {
      font-size: 18px;

      &.el-breadcrumb__inner a:hover,
      .el-breadcrumb__inner.is-link:hover {
        color: inherit;
      }
    }
  }

  :deep(.el-breadcrumb__inner is-link) {
    color: #333;
  }

  :deep(.breadcrumb) {
    padding-top: 65px;
    padding-bottom: 40px;
  }

  :deep(.econtainer) {
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);
    //letter-spacing: 1.5px;
  }

  .el-breadcrumb :deep(.el-breadcrumb__item) {
    font-size: 18px;
  }

  .el-breadcrumb :deep(.el-breadcrumb__separator) {
    font-size: 18px;
  }

  .content {
    background: #f0f2f5;
  }

  .news-list {
    padding-bottom: 20px;
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
  }

  .item {
    padding-top: 100px;
    padding-bottom: 30px;

    .time {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      letter-spacing: 1px;
      font-weight: 700;
      text-align: center;
      padding-top: 40px;
    }

    .title {
      font-size: 30px;
      font-weight: 700;
      text-align: center;
    }

    .sub-title {
      font-size: 24px;
      font-weight: 700;
    }

    .desc {
      padding-top: 50px;

      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      //letter-spacing: 1px;
      text-align: left;
      line-height: 28px;
      text-indent: 2em;

      :deep(p) {
        white-space: pre-wrap;
      }
    }

    .img-box {
      max-width: 935px;
      margin: 0 auto;
      margin-top: 30px;
      margin-bottom: 30px;

      .img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
  }
}

@media (max-width: 1024px) {
  .news-details .breadcrumb {
    padding: 30px 20px 20px;
  }

  .news-details {
    padding: 30px 30px 0 30px;
  }

  .news-details .title {
    font-size: 28px;
  }

  .news-details .sub-title {
    font-size: 22px;
  }
}

@media (max-width: 820px) {
  .news-details .title {
    font-size: 28px;
  }

  .news-details .sub-title {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  .news-details .sub-title {
    font-size: 18px;
  }
}

@media (max-width: 625px) {
  .news-details .title {
    font-size: 24px;
  }

  .news-details .sub-title {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .news-details .title {
    font-size: 20px;
  }

  .news-details .sub-title {
    font-size: 12px;
  }

  .news-details .desc {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .news-details .title {
    font-size: 18px;
  }

  .news-details .sub-title {
    font-size: 12px;
  }

  .news-details .el-breadcrumb .el-breadcrumb__item {
    font-size: 16px;
  }

  .news-details .item {
    padding-top: 50px;
  }

  .news-details .desc {
    font-size: 16px;
  }

  .news-details .breadcrumb {
    padding: 30px 20px 20px;
  }
}

@media (max-width: 414px) {
  .news-details .breadcrumb {
    padding: 30px 15px 20px;
  }
}

@media (max-width: 390px) {
  .news-details .el-breadcrumb .el-breadcrumb__item {
    font-size: 15px;
  }
  .news-details {
    padding: 20px;
  }

  .news-details .title {
    font-size: 17px;
  }

  .news-details .sub-title {
    font-size: 12px;
  }

  .news-details .desc {
    font-size: 14px;
  }

  .news-details .breadcrumb {
    padding: 30px 20px 20px;
  }
}

@media (max-width: 375px) {
  .news-details .breadcrumb {
    padding: 30px 15px 20px;
  }
}
</style>
