.tangka-main {
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  max-width: 1920px;
  width: 100vw;
  margin: 0 auto;
  :deep(.el-carousel__item) {
    overflow-x: auto;

    &::-webkit-scrollbar {
        width: 6px;
        height: 8px;
    }

    &::-webkit-scrollbar-track {
        background: #dddddd;
    }

    &::-webkit-scrollbar-thumb {
        background: #999999;
        border-radius: 6px;
    }
  }

  :deep(.el-carousel__item:not(.is-active)) {
    opacity: 0.4;
  }
  .top-desc {
    height: 65px;
    display: flex;
    gap: 35px;
    justify-content: center;
    align-items: center;
    background: #f2f3f5;
    position: sticky;
    top: 0;
    // background: #f2f3f5;
    z-index: 999;
    
    .view {
      display: flex;
      gap: 30px;

      .item {
        position: relative;
        cursor: pointer;
      }
    }

    .item-at {
      font-weight: 600;
      font-size: 16px;
      color: #1f2a72;
    }
  }

  .banner-view {
    .banner-main {
      overflow: hidden;

      .banner-main-carousel {
        height: 100%;
        :deep(.el-carousel__container) {
          height: 100%;
        }

        :deep(.el-carousel__arrow) {
          font-size: 65px;
          color: #ffffff;
          background: none;
        }

        .banner-main-carousel-item {
          .carousel-image {
            width: 100%;
            height: 700px;
          }
        }
      }
    }
  }
  #tangkaPart1_1 .preface {
    display: flex;
    flex-direction: column;
    padding-top: calc(7.9vw);   
    align-items: center;

    .title {     
      font-size: 36px;
      font-weight: 600;
    }
    .content {
      margin: auto calc(22.34vmin);
      margin-top: calc(2.66vmin);

      p {
        font-size: 18px;
        text-indent: 2em;
      }

    }
  }
  #tangkaPart1_1.content p :not(:first-child){
        margin-top: 2em;
  }
  .character-info {
    // background: #ffffff;

    .character-main {
      display: flex;
      flex-direction: column;
      
      padding-top: 100px;
      padding-bottom: 100px;
    }

    .desc-view {
      // max-width: 1320px;
      width: 80%;
      margin: 0 auto;

      .desc {
        display: flex;
       

        .txt-view {
          display: flex;
          align-items: center;

          .txt {
            display: flex;
            flex-direction: column;
          }

          h1 {
            // font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 600;
            font-size: 36px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 2rem;
            letter-spacing: 0.1rem;
            margin-bottom: 2rem;
          }

          p {
            font-weight: 400;
            font-size: 16px;
            color: #000000;
            line-height: 24px;            
          }
        }
        .txt-view-lifetree {
          display: flex;
          flex-direction: row;
          width: 80%;
          margin: 0 auto;
          padding-bottom: 20%;          

          .img {
            padding-left: 2rem;
            .img {
              margin-top: 1rem;
              width: 358.98px;              
            }
            p {
              text-align: center;
              margin-top: 2rem;
              font-size: 1rem;
              font-weight: 600;
            }
          }
          .txt {
            display: flex;
            flex-direction: column;
            line-height: 3rem;
            letter-spacing: 0.2rem;           
            
           
            color: rgba(0, 0, 0, 0.85);

            h3 {
              font-size: 28px;
              span {                
                font-weight: 600;
                font-size: 36px;                
              }
            }

            p {
              font-weight: 400;
              font-size: 1rem;
              color: #000000;
              line-height: 1.5rem;
              margin-top: 2rem;
            }
          }
        }
      }
    }

    .user-info {
      .desc {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        // margin: 0 10%;
        
        .txt-view {
          padding-left: 10%;
        }
      }
    }

    .desc-view-i2 {
      align-items: flex-start;

      .desc {
        // max-width: 1320px;
        width: 100%;
        margin: 0 auto;

        display: flex;
        flex-direction: column;

        .txt-view {
          
          .txt {
            align-items: flex-start;            
          }
        }

        .img-view-i2 {
          .tangka-certificate {
            // height: 100%;
            // width: 100%;
            margin: 50px auto 0 auto;        

           
            // .certificate-carousel,
            // .lifetree-carousel,
            // .museum-carousel {
            //    padding: 10% 10%;              

            //   .certificate-item {
            //     display: flex;
            //     flex-direction: column;
            //     height: 280px;

            //     .certificate-img {
            //       width: 80%;
            //       margin: 0 auto;
            //       margin-bottom: -2%;              
            //     }
            //     .certificate-img-i {
            //       z-index: 999;
            //       // .i-3 { margin:0 auto; }
            //     }
            //     .certificate-img-bm {
            //       z-index: 999;
            //     }
            //   }
            //   .lifetree-item {
            //     height: 300px;
            //     width:auto;
            //     margin: 0 auto;

            //     .lifetree-img {
            //      height: 300px;
            //      width:auto;
            //       margin: 0 auto;
            //       .img  {
            //         width: auto;
            //         height: 300px;
            //       }
            //     }
            //   }
            //   .museum-item{              
            //     height: 612px;
            //     .lifetree-img {
            //      height: 300px;
            //      width:auto;
            //       margin: 0 auto;
            //       .img  {
            //         width: auto;
            //         height: 300px;
            //       }
            //     }              
            //   }
            // }
            
            // .lifetree-carousel {
            //   margin-left: -10%;
            //   margin-right: -10%;
            // }
          }
        }
      }
    }

    .desc-view-i3 {
      align-items: flex-end;

      .desc {
        width: 717px;

        .txt-view {
          .txt {
            align-items: flex-start;
          }
        }
      }
    }
  }

  .time-line-info {
    // background: #ffffff;

    .time-line-main {
      width: 100%;
      margin: 0 auto;
      padding-top: 100px;
      padding-bottom: 100px;

      .title {
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 600;
        font-size: 36px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 54px;
        letter-spacing: 3px;
        text-align: center;
      }

      .time-line-view {
        margin-top: 52px;

        overflow: hidden;

        .content-main {
          display: flex;
        }

        .content {
          width: max-content;
          /* //padding-right: 300px; */

          .desc {
            display: flex;           
            width: auto;
            padding-left: 467px;

            .item {
              width: 467px;
              min-width: 467px;

              h3 {
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 400;
                font-size: 24px;
                color: #000000;
                line-height: 28px;
                margin-bottom: 6px;
              }

              h6 {
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 19px;
                color: #666666;
              }

              div {
                margin-top: 16px;

                p {
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 400;
                  font-size: 16px;
                  color: #000000;
                  line-height: 32px;
                }
              }
            }
          }

          .desc-bottom {
            margin-left: 467px;
          }

          .line {
            height: 38px;

            &::before {
              content: "";
              display: block;
              width: 100%;
              height: 2px;
              background: #1f2a72;
              opacity: 0.2;
              position: relative;
              top: 18px;
            }
          }
        }

        /* .contentSecond {
          .desc {
            // padding-left: 0;
            // padding-right: 0;
          }
        } */
      }

      .scroll-view {
        .content-main {
          width: fit-content;
          animation: listScrolling 40s linear infinite;

          @keyframes listScrolling {
            0% {
              transform: translateX(0px);
            }
            100% {
              transform: translateX(-50%);
            }
          }

          &:hover {
            animation-play-state: paused;
          }
        }
      }
    }
  }

  .achievement-info {
    // background: #ffffff;

    .achievement-info-main {     
      width: 80%;
      margin: 0 auto;
      padding-top: 10px;
      padding-bottom: 10%;

      .title {
          // font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 600;
          font-size: 3rem;
          color: rgba(0, 0, 0, 0.85);
          // line-height: 1rem;
          letter-spacing: 0.5rem;
          text-align: center;
        }
             
      .achievement-zone {
        margin:5% auto;
        display: flex;
        flex-direction: row;
        // width: 80%;

        .achievement-view {
          margin-top: 1rem;
           margin-right: 3rem;     
           max-height: 300px;
           overflow-y: auto;

          .item {
            display: flex;
           
            margin-bottom: 2rem;
            padding-right: 2rem;

            .time {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 600;
              font-size: 1rem;
              color: #000000;
              line-height: 32px;
              width: 8%;
              text-align: left;
            }

            .desc {
              flex: 1;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              font-size: 1rem;
              color: #000000;
              line-height: 1.2rem;
            }
          }
        }

         .achievement-right-wrap {
          .wrap-box {
            margin: 5% auto;
            cursor: pointer;
            .dateTabItemAcitve {
              font-size: 3rem;
              line-height: 3rem;
              color:#ff6a00;
              font-weight: 600;
            }
            .dateTabItem {
              font-size: 3rem;
              line-height: 3rem;
              color: gainsboro;
              font-weight: 600;              
            }
            .button-wrap {
              display: flex;
              flex-direction: row;
              height: 2.5rem;
              position:sticky;              
              width: 100%;             
             
              .up {                   
                box-sizing: border-box;
                cursor: pointer;
                height: 2.25rem;
                width: 2.25rem;
                background: url(data:image/png;base64,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) 50%/cover no-repeat;
                background-size: 100%;
                margin: 0 auto;                
              }
              .down {                   
                box-sizing: border-box;
                cursor: pointer;
                height: 2.25rem;
                width: 2.25rem;               
                background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADUAAAA1CAYAAADh5qNwAAAHEElEQVR4AdXBb2jehZ0A8M/3l9o0LeHaiLMvQvfiOMO9WDuYHpUDPez+lHObba9XuDyp8yqCFFKRE+eW3Uym1bK9KDVcX1SbSdMUzivpeeyOMrgJDjGggraMLdU3V8OwaP9IVtO0Lt/LQ/rjiaG1fZJspp9PmEe5TbOL7pLWSm1oE26RmtGEMWFU+hDDwrAwZLFXo8+oeRLmKNutUNgiVaQ7sUj9PhVeFwYs8u/xonPmIMxSPqjFmC5hu7TEfAkXpL2a7Iz9zpiFUKd8VJNTduAH+AszhRG8jDcwrNFJjFrvE0ctRbNxq9CGO3Cf1GqmcE7a5VbPxW5j6hDqkO02CXukVp91AoeF/3TQmxHSdcoUOtwubcBm3Ga6MCI9EocMuk7hOmQKFT34V9OFk+hy0ECENEeZQocKdkqrfNZTBjwZIV1DuIbcapkJB6RNas7iGTfrjV7j5ll2anRap9AlLVcKgwr3R7/zPkf4HFnRil9Ia5TCkJtsjBd94E8sH7DSJUektUrhHXw7Boy4igZXkVstk/5XWqMU+rX4h9jnY38GPW/7Q/ffOeiCL2ONKSuFdd1f1d9zzCVXEK4gU+hwWNqkKkzgBzHgp74gWfE4npUKVWHQQZsjpBkaXEH3u36Ch5XCEzHgp75APce91r3aBXzDlL82qOg57hUzNJgh223CvymF/hjwmAWg57jXulf7S6wx5e7urzjec9xvTVOYJh/VJOxRCkNaPGQhafGQMKQU9uSjmkxTmO6UHVKrKWfdZGP0GreARK9xN9mIs6pSq1N2mKZwWT6oRXhCzTPxog8sQPGiD/CMUngiH9TiskJpTJe0XFX4PzfrtZDdrFc4qSotN6bLZYVJ2W6FsF3Nj6LXuAUseo2jSylsz3YrTCpUFbZIS0w54aABN4KDBnBCVVqisMWkQlWqqDkcId0AIiQOK6WKSUVu0yzdqdTgiBtJgyNK6c7cprlw0V1YpCqMOOAtN5ID3hJGTFnkorsKaa2alyOkG0iExMtKae0iqU3NG2YhO2yR/t5f2RbdJtQpuxXe1Sf8Txz0kvq9oZTaCrQpFX6nTlmx0YQB6Xve9XymUIdM4YQXpO+ZMJAVG9VvWE1bIdyidJP31W8rFqlK23TYqx4d9uKfTVmEDvVqdFIp3FJIzWpG1avFPwm/VEoPZ8Ue1yEr9kgPK4VfatGufqNKqblAk9J6n6hT9Br3JRuEV5TSjuzwM58jO/xM2qHmV75kQ/QaV6/1PlHTVGBM6ailZiF2G1P4jvBrpQmPZcXTriArnjbhMaXwaw2+G7uNmY2jlqoZK4RRNc1mKfqd1+heYUgpdWXFj02TFT+WupTCkEb3Rr/zZq9ZKYwW0odK41aZg+gzapn1eFMp9WS775uU7b4v9ah50zLro8+ouRi3Sil9WGBYTZs5in0+1uRbwjtqdmW7/8IupfA2vhn7fGzu2tQMF8KwmjvMg9jvjGW+jt+o+Y6a31jmG3HIWfPjb5TCcCEMqbkvU5gHsc9HllonDPus31lqXezzkXmQKfBdpTBUWOxVfKoqtepwu3kSLzil0T3Ce6rCe5ZYFy84Zb50uF1qNeVTi71aRJ9R4XWltME8ij6/t9g9wisWuyf6/N58ShuUwuvRZ7RQFQbUbM4U5lH83Psx4J74uffNo0yBzUphwKRC1YSXhAum3KZDxY2gQwW3qQoXTHjJpMKkOOSstFfN09mp0QKWnRqxUyntjUPOmlQoNdkpnFOVvuy0TgvZaZ3SKlXhnCY7XVa4LPY7I+1S88N8wEoLUD5gpdCllHbFfmdcVpjuVs8JI6ascMmR7NRoAclOjS45Ii1XFUbc6jnTFKaJ3cakR5TSWmc8byE543lprVJ6JHYbM02DGXqO+233VzTgblPWdK821nPca75gWfG49C9qnopD9pqhcCUDnhQG1TybFY/7AmXF43hWKQwa8KQrCFeRWy0z4TVpjVLo1+Kh6DXuzyQ7NTrjeWmrUnhH4W+j33lX0OAqeo651L3aL4R1WGnKGhd8vftr/rvnbX/wJ5YPWOm8o9K9SuEdfDsOOu0qwjXkVstMOCBtUnMWz7hZb/QaN8+yU6PTOvFDrFAKgwr3R7/zPkeDa+g55lL3Mf9hUIG7TWnCN12wtXu1j7qPOd7TY84yRfd7Oow5gn9Ek5qnDNgem1x0DaEO2W6TsEdq9VkncFiDIw54K0K6TpnC/b7mjzZiM24zXRiRHolDBl2nUKd8VJNTdghPSMvNFEbwMt7AsEYnMWq9Txy1FM3GrUIb7sB9UquZwjlpl1s9F7uNqUOYpXxQizFdwnZpifkSLkh7NdkZ+50xC2GOst0KhS1SRboTi9TvU+F1YcCEl+KQs+YgzKPcptlFd0lrpTa0CbdIzWjCmDAqfYhhYVgYstir0WfUPPl/lL1tshEnFFMAAAAASUVORK5CYII=) 50%/cover no-repeat;
                background-size: 100%;
                margin: 0 auto;
              }
            }
            .showYear {
              text-align: right;
              margin-top: 30px;
              margin-bottom: 30px;
            }
          }
        }
      }
    }
  }
  // .lifeTree-info {
  //   // background: #ffffff;   
  //   margin: 0 auto;

  //   .lifeTree-main {
  //     max-width: 1320px;
  //     width: 80%;
  //     margin: 0 auto;
  //     display: flex;
  //     flex-direction: row;
      
  //     p {
  //       margin-top: 2rem;
  //       margin-left: auto;
  //       margin-right: auto;
  //       font-size: 1rem;
  //       line-height: 1.2rem;
  //     }
  //     h1{ 
  //       font-size: 24px;
        
  //       span {
  //         font-size: 36px;
  //         font-weight: 600;
  //     }
  //     }
  //     .txt {
  //       margin: 0 auto 120px auto;
  //       padding-right: 120px;
  //     }
      
  //   }
  // }
  .certificate-carousel,
  .lifetree-carousel,
  .museum-carousel {
    padding: 10% 10%;

    .certificate-item {
      display: flex;
      flex-direction: column;
      height: 280px;

      .certificate-img {
        width: 80%;
        margin: 0 auto;
        margin-bottom: -2%;
      }

      .certificate-img-i {
        z-index: 999;
        // .i-3 { margin:0 auto; }
      }

      .certificate-img-bm {
        z-index: 999;
      }
    }

    .lifetree-item {
      height: 360px;
      width: auto;
      margin: 0 auto;

      .lifetree-img {
        height: 360px;
        width: auto;
        margin: 0 auto;

        .img {
          width: auto;
          height: 360px;
        }
      }
    }

    .museum-item {
      height: 612px;

      .lifetree-img {
        height: 300px;
        width: auto;
        margin: 0 auto;

        .img {
          width: auto;
          height: 300px;
        }
      }
    }
  }

  .lifetree-carousel {
    width: 125%; 
  }
  #tangkaPart3  {
    :deep(.el-carousel__arrow) {
        /* 将箭头位置从默认的50%上调到30% */
         top: 40%;
        /* 可选：调整箭头大小 */
        width: 2.5rem;
        height: 2.5rem;
        /* 可选：调整箭头颜色 */               
        
        
    }
    :deep(.el-carousel__arrow--left) {
      margin-left: +5%;
      color:#000000;
      background-color: white;
      border: solid 2px gray;
    }
    :deep(.el-carousel__arrow--right) {
      margin-right: +5%;
      color: white;
      background-color:dodgerblue;
      border: 0;
    }
  } 
  
  // lifetree
  #tangkaPart5  {
    :deep(.el-carousel__arrow) {
        /* 将箭头位置从默认的50%上调到30% */
        //  top: 40%;
        /* 可选：调整箭头大小 */
        width: 4rem;
        height: 4rem;
        /* 可选：调整箭头颜色 */               
        color:#000000;
        background-color: white;        
    }
    :deep(.el-carousel__arrow--left) {
      margin-left: +10%;      
    }
    :deep(.el-carousel__arrow--right) {
      margin-right: +25%;     
    }
    .desc-view {
      width: 100%;
    }
    .lifetree-carousel {
      padding: 0 0;
    }
  } 

}

@media (max-width: 1320px) {
  .tangka-main .works-info,
  .tangka-main .character-info .character-main,
  .tangka-main .achievement-info .achievement-info-main,
  .tangka-main .time-line-info .time-line-main {
    padding-left: 20px;
    padding-right: 20px;
  }

  .tangka-main .banner-view .banner-main .banner-main-carousel .banner-main-carousel-item .carousel-image {
    width: 80%;
    height: auto;
  }
 
   #tangkaPart4 .txt-view-lifetree {
    width: 100%;
  }
 

 
  
}

@media (max-width: 1200px) {
  .tangka-main .character-info .user-info .desc {
    flex-direction: column;   
  }
  .tangka-main .character-info .desc-view .desc .txt-view {
    align-items: center;
    justify-content: center;
  }
  .tangka-main .character-info .desc-view .desc .txt-view .txt {
    align-items: center;
  }
  .tangka-main .character-info .user-info .desc .img-view {
    width: 100%;
    margin: 0 auto;
  }
  #tangkaPart5 .character-main{
    width: 80%;
    margin: 0 auto;
  }
  #tangkaPart5 .txt-view-lifetree{
    width: 100%;
  }
}

@media (max-width: 1024px) {
  .tangka-main .character-info .desc-view .desc {
    width: 100%;
  }

  .tangka-main .character-info .desc-view .desc .txt-view {
    align-items: center;
  }

  .tangka-main .character-info .desc-view-i3 .desc .txt-view .txt {
    align-items: flex-start;
  }

  .tangka-main .top-desc {
    overflow-x: auto;
    white-space: nowrap;
  }
  // .tangka-main .top-desc {
  //   align-items: left;
  //   // margin-left: 20px;
  //   // margin-right: 20px;
  // }
  // #tangkaPart1_1 .preface .content {
  //   width: 100%;
  //   padding-left: 200px;
  //   padding-right: 200px;
  // }
 
  //  #tangkaPart2 .desc {
  //   flex-direction: column;
  // }
  
  // #tangkaPart3 .certificate-item {
  //   height: 180px;
  //   width: auto;

  //   .certificate-img {
  //     height: 80%;
  //     width: 80%;
  //     margin: 0 auto;
  //   }

  //   .certificate-img-bottom {
  //     width: 90%;
  //     margin-bottom: - 30px;
  //     z-index: 999;
  //   }
  // }
  // #tangkaPart4 {
  //   .achievement-view {
  //     margin: auto 0 0 0;
  //   }
  // }
  // #tangkaPart5 .lifetree-item {
  //   height: 350px;
  //   width: auto;
  //   margin: 0 auto;

  //   .lifetree-img {
  //     height: 350px;
  //     width: auto;
  //     margin: 0 auto;

  //     .img {
  //       width: auto;
  //       height: 350px;
  //     }
  //   }
  // }
  // #tangkaPart5 .txt-view-lifetree {
  //   display: flex;
  //   flex-direction: column;
  //   .txt {
  //     padding-right: 0px;
  //   }
  //   .img.img{
  //     margin: 0 auto;
  //   }
  // }

  // #tangkaPart6 .museum-item {
  //   height: 612px;

  //   .museum-img {
  //     height: 300px;
  //     width: auto;
  //     margin: 0 auto;

  //     .img {
  //       width: auto;
  //       height: 300px;
  //     }
  //   }
  // }

  // .certificate-carousel {
  //   height: 150px;
  // }

  // // .lifetree-carousel {
  // //   height: 150px;
  // // }

  // .museum-carousel {
  //   height: 400px;
  // }
  
}


@media (max-width: 820px) {
  .tangka-main .time-line-info .time-line-main .time-line-view .content .desc {
    flex-direction: column;
    gap: 60px;
    padding-left: 0px;
  }

  .tangka-main .time-line-info .time-line-main .time-line-view .content .desc .item {
    width: calc(100vw - 40px);
    min-width: calc(100vw - 40px);
  }
  .preface .content {
    padding-left: 300px;
    padding-right: 300px;
  }

  #tangkaPart2 div.img-view {
    width: 100%;
  }
  #tangkaPart2 div.txt-view {
     margin-left: 40px;
     margin-right: 40px;
  }
#tangkaPart3 .certificate-item {
  height: 180px;
  width: auto;

  .certificate-img {
    height: 80%;
    width: 80%;
    margin: 0 auto;
  }

  .certificate-img-bottom {
    width: 90%;
    margin-bottom: - 30px;
    z-index: 999;
  }
}

#tangkaPart5 .lifetree-item {
  height: 150px;
  width: auto;
  margin: 0 auto;

  .lifetree-img {
    height: 150px;
    width: auto;
    margin: 0 auto;

    .img {
      width: auto;
      height: 150px;
    }
  }
}

#tangkaPart6 .museum-item {
  height: 612px;

  .museum-img {
    height: 300px;
    width: auto;
    margin: 0 auto;

    .img {
      width: auto;
      height: 300px;
    }
  }
}

.certificate-carousel {
  height: 150px;
}

.lifetree-carousel {
  height: 150px;
}

.museum-carousel {
  height: 350px;
}
}

 
@media (max-width: 768px) {
  .tangka-main .banner-view .banner-main .banner-main-carousel {
    :deep(.el-carousel__arrow) {
      font-size: 42px;
    }
  }

  .tangka-main .character-info .desc-view-i2 .desc .img-view-i2 {
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .tangka-main .character-info .desc-view-i2 .desc .img-view-i2 .tangka-certificate .certificate-carousel {
    padding: 0 20px;
  }

  .tangka-main .character-info .desc-view-i2 .desc .txt-view .txt {
    display: flex;
    flex-direction: column;
    /* // align-items: flex-start; */
  }

  .tangka-main .achievement-info .achievement-info-main .achievement-view .item {
    flex-direction: column;
    gap: 20px;
  }

  .tangka-main .achievement-info .achievement-info-main .achievement-view .item .time {
    text-align: left;
  }

  .tangka-main .achievement-info .achievement-info-main .achievement-view .item .time::before {
    left: 75px;
  }

  .tangka-main .character-info .desc-view .desc {
    align-items: center;
  }

  .tangka-main .character-info .user-info .desc .img-view {
    width: 100%;
  }

  #tangkaPart6 .museum-item {
    height: 150px;

    .museum-img {
      height: 150px;
      width: auto;
      margin: 0 auto;

      .img {
        width: auto;
        height: 150px;
      }
    }
  }  
  .museum-carousel {
    height: 150px;
  }

  #tangkaPart2.character-info div.desc {
    margin: 0 auto;
  }

}

@media (max-width: 576px) {
  .tangka-main .character-info .desc-view-i2 .desc .img-view-i2 .img {
    width: 100%;
  }

  
  #tangkaPart5 .lifetree-item {
    height: 100px;
    width: auto;
    margin: 0 auto;

    .lifetree-img {
      height: 100px;
      width: auto;
      margin: 0 auto;

      .img {
        width: auto;
        height: 100px;
      }
    }
  }
  #tangkaPart5 .txt-view-lifetree {
    display: flex;
    flex-direction: column;
    .txt {
      padding-right: 0px;
    }
    .img.img{
      margin: 0 auto;
      height: 100px;
    }
  }

  #tangkaPart6 .museum-item {
    height: 150px;

    .museum-img {
      height: 150px;
      width: auto;
      margin: 0 auto;

      .img {
        width: auto;
        height: 150px;
      }
    }
  }

  .certificate-carousel {
    height: 80px;
  }
  

  .lifetree-carousel {
    margin-top: 300px;
    height: 100px;
  }

  .museum-carousel {
    height: 150px;
  }
}
@media (max-width: 480px) {
  .tangka-main .character-info .character-main {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .tangka-main .character-info .desc-view .desc .txt-view h3 {
    font-size: 24px;
  }

  .tangka-main .time-line-info .time-line-main .time-line-view .content .desc {
    padding-left: 0px;
  }

  .tangka-main .character-info .desc-view .desc .txt-view .txt {
    align-items: flex-start;
  }

  .tangka-main .time-line-info .time-line-main .title {
    font-size: 32px;
  }

  .tangka-main .top-desc {
    margin-left: 20px;
    margin-right: 20px;
  }

  .tangka-main .time-line-info .time-line-main {
    padding-left: 20px;
    padding-right: 20px;
  }

  .tangka-main .time-line-info .time-line-main .time-line-view .content .line {
    display: none;
  }

  .tangka-main .time-line-info .time-line-main .time-line-view .content .desc-bottom {
    margin-left: 0px;
    padding-top: 60px;
    gap: 60px;
  }

  .tangka-main .time-line-info .time-line-main {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .tangka-main .achievement-info .achievement-info-main {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .tangka-main .time-line-info .time-line-main .time-line-view .content .desc .item h3 {
    font-size: 22px;
  }

  .tangka-main .achievement-info .achievement-info-main .title {
    font-size: 32px;
  }

  .tangka-main .time-line-info .time-line-main .title,
  .tangka-main .achievement-info .achievement-info-main .title {
    font-size: 30px;
  }
  #tangkaPart5 .lifetree-item {
    height: 80px;
    width: auto;
    margin: 0 auto;

    .lifetree-img {
      height: 80px;
      width: auto;
      margin: 0 auto;

      .img {
        width: auto;
        height: 80px;
      }
    }
  }
  #tangkaPart5 .txt-view-lifetree {
    display: flex;
    flex-direction: column;
    .txt {
      padding-right: 0px;
    }
    .img.img{
      margin: 0 auto;
      height: 100px;
    }
  }

  #tangkaPart6 .museum-item {
    height: 150px;

    .museum-img {
      height: 150px;
      width: auto;
      margin: 0 auto;

      .img {
        width: auto;
        height: 150px;
      }
    }
  }

  .certificate-carousel {
    height: 80px;
  }
  

  .lifetree-carousel {
    margin-top: 300px;
    height: 80px;
  }

  .museum-carousel {
    height: 150px;
  }
}

@media (max-width: 390px) {
  .tangka-main .banner-view .banner-main .banner-main-carousel {
    height: 210px;
  }

  .tangka-main .time-line-info .time-line-main .title,
  .tangka-main .achievement-info .achievement-info-main .title {
    font-size: 28px;
  }

  .tangka-main .time-line-info .time-line-main .time-line-view .content .desc .item h3 {
    font-size: 22px;
  }
}
@media (max-width: 380px) {
}
