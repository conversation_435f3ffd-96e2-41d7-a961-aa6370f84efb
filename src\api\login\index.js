/*
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-04-22 15:12:39
 */

import { request } from "@/utils/service.js"

/** 获取登录验证码 */
export function getLoginCodeApi() {
  return request({
    url: "login/code",
    method: "get"
  })
}

/** 登录并返回 Token */
export function loginApi(data) {
  return request({
    url: "user/login",
    method: "post",
    data
  })
}

/** 获取用户详情 */
export function getUserInfoApi() {
  return request({
    url: "user/info",
    method: "get"
  })
}
