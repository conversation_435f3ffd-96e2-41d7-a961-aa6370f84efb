/**
 * 增强的响应式 Hook
 * 提供更全面的响应式功能，包括断点检测、设备类型判断、媒体查询监听等
 */

import { ref, computed, readonly, onMounted, onBeforeUnmount } from 'vue'
import { 
  BREAKPOINTS, 
  DEVICE_BREAKPOINTS, 
  MEDIA_QUERIES, 
  DEVICE_TYPES,
  getCurrentDeviceType,
  isMobileDevice,
  isTabletDevice,
  isDesktopDevice,
  createResponsiveListener
} from '@/constants/responsive'

/**
 * 响应式断点 Hook
 * @returns {Object} 响应式断点状态和工具函数
 */
export function useBreakpoints() {
  // 当前窗口宽度
  const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1200)
  
  // 断点状态
  const breakpoints = computed(() => ({
    xs: windowWidth.value >= BREAKPOINTS.XS,
    sm: windowWidth.value >= BREAKPOINTS.SM,
    md: windowWidth.value >= BREAKPOINTS.MD,
    lg: windowWidth.value >= BREAKPOINTS.LG,
    xl: windowWidth.value >= BREAKPOINTS.XL,
    xxl: windowWidth.value >= BREAKPOINTS.XXL,
    xxxl: windowWidth.value >= BREAKPOINTS.XXXL,
  }))
  
  // 当前断点
  const currentBreakpoint = computed(() => {
    const width = windowWidth.value
    if (width < BREAKPOINTS.XS) return 'xs'
    if (width < BREAKPOINTS.SM) return 'sm'
    if (width < BREAKPOINTS.MD) return 'md'
    if (width < BREAKPOINTS.LG) return 'lg'
    if (width < BREAKPOINTS.XL) return 'xl'
    if (width < BREAKPOINTS.XXL) return 'xxl'
    return 'xxxl'
  })
  
  // 设备类型
  const deviceType = computed(() => getCurrentDeviceType())
  
  // 设备类型判断
  const isMobile = computed(() => deviceType.value === DEVICE_TYPES.MOBILE)
  const isTablet = computed(() => deviceType.value === DEVICE_TYPES.TABLET)
  const isDesktop = computed(() => deviceType.value === DEVICE_TYPES.DESKTOP)
  
  // 更新窗口宽度
  const updateWindowWidth = () => {
    if (typeof window !== 'undefined') {
      windowWidth.value = window.innerWidth
    }
  }
  
  // 监听窗口大小变化
  onMounted(() => {
    if (typeof window !== 'undefined') {
      updateWindowWidth()
      window.addEventListener('resize', updateWindowWidth)
    }
  })
  
  onBeforeUnmount(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', updateWindowWidth)
    }
  })
  
  return {
    windowWidth: readonly(windowWidth),
    breakpoints: readonly(breakpoints),
    currentBreakpoint: readonly(currentBreakpoint),
    deviceType: readonly(deviceType),
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),
  }
}

/**
 * 媒体查询 Hook
 * @param {string} query - 媒体查询字符串
 * @returns {Object} 媒体查询匹配状态
 */
export function useMediaQuery(query) {
  const matches = ref(false)
  let mediaQuery = null
  
  const updateMatches = () => {
    if (mediaQuery) {
      matches.value = mediaQuery.matches
    }
  }
  
  onMounted(() => {
    if (typeof window !== 'undefined') {
      mediaQuery = window.matchMedia(query)
      updateMatches()
      mediaQuery.addListener(updateMatches)
    }
  })
  
  onBeforeUnmount(() => {
    if (mediaQuery) {
      mediaQuery.removeListener(updateMatches)
    }
  })
  
  return readonly(matches)
}

/**
 * 预定义媒体查询 Hook
 * @returns {Object} 常用媒体查询状态
 */
export function useCommonMediaQueries() {
  return {
    isMobile: useMediaQuery(MEDIA_QUERIES.MOBILE),
    isTablet: useMediaQuery(MEDIA_QUERIES.TABLET),
    isDesktop: useMediaQuery(MEDIA_QUERIES.DESKTOP),
    isMobileSmall: useMediaQuery(MEDIA_QUERIES.MOBILE_SMALL),
    isMobileLarge: useMediaQuery(MEDIA_QUERIES.MOBILE_LARGE),
    isRetina: useMediaQuery(MEDIA_QUERIES.RETINA),
    
    // 最小宽度查询
    minSm: useMediaQuery(MEDIA_QUERIES.MIN_SM),
    minMd: useMediaQuery(MEDIA_QUERIES.MIN_MD),
    minLg: useMediaQuery(MEDIA_QUERIES.MIN_LG),
    minXl: useMediaQuery(MEDIA_QUERIES.MIN_XL),
    
    // 最大宽度查询
    maxSm: useMediaQuery(MEDIA_QUERIES.MAX_SM),
    maxMd: useMediaQuery(MEDIA_QUERIES.MAX_MD),
    maxLg: useMediaQuery(MEDIA_QUERIES.MAX_LG),
    maxXl: useMediaQuery(MEDIA_QUERIES.MAX_XL),
  }
}

/**
 * 响应式值 Hook
 * 根据不同断点返回不同的值
 * @param {Object} values - 断点值映射对象
 * @returns {ComputedRef} 当前断点对应的值
 */
export function useResponsiveValue(values) {
  const { currentBreakpoint } = useBreakpoints()
  
  return computed(() => {
    const breakpoint = currentBreakpoint.value
    
    // 按优先级查找值
    const priorities = ['xxxl', 'xxl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = priorities.indexOf(breakpoint)
    
    // 从当前断点开始向下查找可用值
    for (let i = currentIndex; i < priorities.length; i++) {
      const key = priorities[i]
      if (values[key] !== undefined) {
        return values[key]
      }
    }
    
    // 如果没有找到，返回默认值或第一个可用值
    return values.default || Object.values(values)[0]
  })
}

/**
 * 响应式类名 Hook
 * 根据设备类型返回不同的类名
 * @param {Object} classNames - 设备类型类名映射
 * @returns {ComputedRef} 当前设备类型对应的类名
 */
export function useResponsiveClass(classNames) {
  const { deviceType } = useBreakpoints()
  
  return computed(() => {
    const type = deviceType.value
    return classNames[type] || classNames.default || ''
  })
}

/**
 * 响应式样式 Hook
 * 根据设备类型返回不同的样式对象
 * @param {Object} styles - 设备类型样式映射
 * @returns {ComputedRef} 当前设备类型对应的样式
 */
export function useResponsiveStyle(styles) {
  const { deviceType } = useBreakpoints()
  
  return computed(() => {
    const type = deviceType.value
    return styles[type] || styles.default || {}
  })
}

/**
 * 响应式监听器 Hook
 * 监听响应式变化并执行回调
 * @param {Function} callback - 响应式变化回调函数
 * @returns {Function} 清理函数
 */
export function useResponsiveListener(callback) {
  let cleanup = null
  
  onMounted(() => {
    cleanup = createResponsiveListener(callback)
  })
  
  onBeforeUnmount(() => {
    if (cleanup) {
      cleanup()
    }
  })
  
  return cleanup
}

/**
 * 响应式容器 Hook
 * 提供响应式容器相关功能
 * @returns {Object} 容器相关的响应式状态和方法
 */
export function useResponsiveContainer() {
  const { deviceType, windowWidth } = useBreakpoints()
  
  // 容器最大宽度
  const maxWidth = computed(() => {
    const width = windowWidth.value
    if (width >= BREAKPOINTS.XXL) return `${BREAKPOINTS.XXL}px`
    if (width >= BREAKPOINTS.XL) return `${BREAKPOINTS.XL}px`
    if (width >= BREAKPOINTS.LG) return `${BREAKPOINTS.LG}px`
    if (width >= BREAKPOINTS.MD) return `${BREAKPOINTS.MD}px`
    if (width >= BREAKPOINTS.SM) return `${BREAKPOINTS.SM}px`
    return '100%'
  })
  
  // 容器内边距
  const padding = computed(() => {
    const type = deviceType.value
    switch (type) {
      case DEVICE_TYPES.MOBILE:
        return '1rem'
      case DEVICE_TYPES.TABLET:
        return '2rem'
      case DEVICE_TYPES.DESKTOP:
        return '4rem'
      default:
        return '1rem'
    }
  })
  
  // 容器样式
  const containerStyle = computed(() => ({
    maxWidth: maxWidth.value,
    paddingLeft: padding.value,
    paddingRight: padding.value,
    marginLeft: 'auto',
    marginRight: 'auto',
  }))
  
  return {
    maxWidth: readonly(maxWidth),
    padding: readonly(padding),
    containerStyle: readonly(containerStyle),
  }
}

// 导出常量以便在模板中使用
export { BREAKPOINTS, DEVICE_BREAKPOINTS, MEDIA_QUERIES, DEVICE_TYPES }

// 默认导出
export default {
  useBreakpoints,
  useMediaQuery,
  useCommonMediaQueries,
  useResponsiveValue,
  useResponsiveClass,
  useResponsiveStyle,
  useResponsiveListener,
  useResponsiveContainer,
}
