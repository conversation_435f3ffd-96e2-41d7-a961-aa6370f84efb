<template>
  <div class="energy-valley">
    <div class="energy-valley-main">
      <div class="info-view">
        <div class="left">
          <el-image class="img" :src="EnergyValley" fit="cover" lazy />
        </div>
        <div class="right" @click="gotoEnergyDetail()">
          <h3 class="science-title">{{ $t("home.energyValley") }}</h3>
          <p class="desc">{{ $t("home.energy_desc1") }}</p>
          <p class="desc">{{ $t("home.energy_desc2") }}</p>
          <p class="desc">{{ $t("home.energy_desc3") }}</p>
          <div class="more-btn">
            <div class="more-view">
              {{ $t("public.learnMore")
              }}<span class="icon"
                ><el-icon><Right /></el-icon
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import EnergyValley from "@/assets/home/<USER>"
import { useRouter } from "vue-router"
const router = useRouter()
const gotoEnergyDetail = () => {
  router.push("/business-segment/energy-valley-details")
}
</script>

<style lang="scss" scoped>
.energy-valley {
  .energy-valley-main {
    width: 1320px;
    max-width: 100%;
    margin: 0 auto;
    padding-top: 120px;

    .title {
      font-size: 40px;
      color: rgba(0, 0, 0);
      text-align: center;
    }

    .info-view {
      display: flex;
      align-items: center;

      &:hover {
        .right {
          background: #409eff;

          .science-title,
          .desc,
          .more-btn {
            color: #ffffff;
          }
        }
      }

      .left {
        width: 800px;
        height: 450px;
        position: relative;
        z-index: 20;

        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);

        .img {
          width: 100%;
          height: 100%;
        }
      }

      .right {
        width: 760px;
        padding: 100px 100px 100px 210px;
        position: relative;
        z-index: 10;
        left: -100px;

        background: #f3f3f3;
        cursor: pointer;

        .science-title {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 25px;
        }
        .desc {
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
          text-align: left;
          letter-spacing: 2px;
        }

        .more-btn {
          display: flex;
          align-items: center;
          margin-top: 40px;
          font-size: 16px;
          font-weight: 600;

          .more-view {
            display: flex;
            align-items: center;
          }
        }

        .icon {
          padding-left: 10px;
          font-size: 20px;

          display: flex;
          align-items: center;
        }
      }
    }
  }
}

@import "./media.scss";
</style>
