# 响应式设计指南

本文档介绍了项目中响应式设计的实现方案和使用方法。

## 概述

我们实现了一套完整的响应式设计系统，包括：

- 统一的断点标准
- 响应式 CSS 混入
- 响应式 Vue 组合式函数
- 响应式指令
- 响应式组件
- 响应式工具函数

## 断点标准

### 基础断点

```javascript
const BREAKPOINTS = {
  XS: 480,      // 超小屏幕 (手机横屏)
  SM: 640,      // 小屏幕 (平板竖屏)
  MD: 768,      // 中等屏幕 (平板横屏)
  LG: 992,      // 大屏幕 (桌面) - 与 Bootstrap 保持一致
  XL: 1200,     // 超大屏幕 (大桌面)
  XXL: 1320,    // 超超大屏幕 (超大桌面)
  XXXL: 1536,   // 极大屏幕
}
```

### 设备类型断点

```javascript
const DEVICE_BREAKPOINTS = {
  MOBILE_MAX: 991,     // 移动端最大宽度
  TABLET_MIN: 768,     // 平板最小宽度
  TABLET_MAX: 1199,    // 平板最大宽度
  DESKTOP_MIN: 1200,   // 桌面最小宽度
}
```

## CSS 混入使用

### 基础响应式混入

```scss
// 最小宽度断点
@include respond-to(lg) {
  // 大屏幕及以上的样式
}

// 最大宽度断点
@include respond-to-max(md) {
  // 中等屏幕及以下的样式
}

// 设备类型
@include mobile-only {
  // 仅移动端样式
}

@include tablet-only {
  // 仅平板端样式
}

@include desktop-only {
  // 仅桌面端样式
}
```

### 响应式工具混入

```scss
// 响应式字体大小
@include responsive-font-size(14px, 16px, 18px);

// 响应式间距
@include responsive-spacing(padding, 1rem, 2rem, 3rem);

// 响应式容器
@include responsive-container;

// 响应式网格
@include responsive-grid(1, 2, 3);

// 响应式 Flexbox
@include responsive-flex(column, row, row);
```

## Vue 组合式函数

### useBreakpoints

```vue
<script setup>
import { useBreakpoints } from '@/hooks/useResponsive'

const { 
  windowWidth, 
  currentBreakpoint, 
  deviceType, 
  isMobile, 
  isTablet, 
  isDesktop 
} = useBreakpoints()
</script>

<template>
  <div>
    <p>当前窗口宽度: {{ windowWidth }}px</p>
    <p>当前断点: {{ currentBreakpoint }}</p>
    <p>设备类型: {{ deviceType }}</p>
    <p v-if="isMobile">移动端显示</p>
  </div>
</template>
```

### useResponsiveValue

```vue
<script setup>
import { useResponsiveValue } from '@/hooks/useResponsive'

const fontSize = useResponsiveValue({
  xs: '14px',
  sm: '16px',
  md: '18px',
  lg: '20px'
})

const columns = useResponsiveValue({
  mobile: 1,
  tablet: 2,
  desktop: 3
})
</script>
```

### useMediaQuery

```vue
<script setup>
import { useMediaQuery } from '@/hooks/useResponsive'

const isMobile = useMediaQuery('(max-width: 991px)')
const isRetina = useMediaQuery('(-webkit-min-device-pixel-ratio: 2)')
</script>
```

## 响应式指令

### v-responsive:show

控制元素的显示/隐藏：

```vue
<template>
  <!-- 只在移动端显示 -->
  <div v-responsive:show="{ mobile: true, desktop: false }">
    移动端内容
  </div>
  
  <!-- 只在桌面端显示 -->
  <div v-responsive:show="{ mobile: false, desktop: true }">
    桌面端内容
  </div>
  
  <!-- 根据断点显示 -->
  <div v-responsive:show="{ xs: false, sm: true, lg: true }">
    小屏幕以上显示
  </div>
</template>
```

### v-responsive:class

动态添加响应式类名：

```vue
<template>
  <div v-responsive:class="{ 
    mobile: 'mobile-style', 
    tablet: 'tablet-style', 
    desktop: 'desktop-style' 
  }">
    响应式类名
  </div>
</template>
```

### v-responsive:style

动态应用响应式样式：

```vue
<template>
  <div v-responsive:style="{ 
    mobile: { fontSize: '14px', padding: '10px' },
    tablet: { fontSize: '16px', padding: '20px' },
    desktop: { fontSize: '18px', padding: '30px' }
  }">
    响应式样式
  </div>
</template>
```

## 响应式组件

### ResponsiveContainer

响应式容器组件：

```vue
<template>
  <ResponsiveContainer 
    :max-width="true"
    :padding="true"
    :center="true"
    type="default"
  >
    <div>容器内容</div>
  </ResponsiveContainer>
</template>
```

### ResponsiveGrid

响应式网格组件：

```vue
<template>
  <ResponsiveGrid 
    :cols="{ xs: 1, sm: 2, md: 3, lg: 4 }"
    :gap="{ xs: '1rem', md: '1.5rem', lg: '2rem' }"
    type="grid"
  >
    <div v-for="i in 8" :key="i" class="grid-item">
      网格项 {{ i }}
    </div>
  </ResponsiveGrid>
</template>
```

## Tailwind CSS 响应式类

### 自定义断点

```css
/* 移动端 */
.mobile:max-lg { }

/* 平板端 */
.tablet:min-md.max-xl { }

/* 桌面端 */
.desktop:min-xl { }
```

### 响应式工具类

```html
<!-- 响应式显示 -->
<div class="show-mobile hide-desktop">移动端显示</div>
<div class="hide-mobile show-desktop">桌面端显示</div>

<!-- 响应式容器 -->
<div class="container-responsive">响应式容器</div>

<!-- 响应式内边距 -->
<div class="px-responsive">响应式内边距</div>

<!-- 响应式文本对齐 -->
<div class="text-center-mobile text-left-desktop">响应式对齐</div>
```

## 工具函数

### 响应式管理器

```javascript
import { responsiveManager } from '@/utils/responsive'

// 获取当前断点
const currentBreakpoint = responsiveManager.getCurrentBreakpoint()

// 添加断点变化监听器
responsiveManager.addListener('my-listener', (newBreakpoint, oldBreakpoint) => {
  console.log(`断点从 ${oldBreakpoint} 变为 ${newBreakpoint}`)
})

// 移除监听器
responsiveManager.removeListener('my-listener')
```

### 响应式值计算

```javascript
import { ResponsiveValueCalculator } from '@/utils/responsive'

const fontSize = ResponsiveValueCalculator.calculate({
  xs: '14px',
  sm: '16px',
  md: '18px',
  lg: '20px'
})

// 插值计算
const interpolatedValue = ResponsiveValueCalculator.interpolate(
  14, 20, 480, 1200, window.innerWidth
)
```

## 最佳实践

### 1. 优先使用混入和组合式函数

```scss
// ✅ 推荐
.my-component {
  @include responsive-font-size(14px, 16px, 18px);
  @include responsive-spacing(padding, 1rem, 2rem, 3rem);
}

// ❌ 不推荐
.my-component {
  font-size: 14px;
  padding: 1rem;
  
  @media (min-width: 768px) {
    font-size: 16px;
    padding: 2rem;
  }
  
  @media (min-width: 1200px) {
    font-size: 18px;
    padding: 3rem;
  }
}
```

### 2. 移动端优先设计

```scss
// ✅ 推荐 - 移动端优先
.component {
  // 移动端样式
  font-size: 14px;
  padding: 1rem;
  
  @include respond-to(md) {
    // 平板端样式
    font-size: 16px;
    padding: 2rem;
  }
  
  @include respond-to(lg) {
    // 桌面端样式
    font-size: 18px;
    padding: 3rem;
  }
}
```

### 3. 使用语义化的断点

```vue
<script setup>
// ✅ 推荐
const columns = useResponsiveValue({
  mobile: 1,
  tablet: 2,
  desktop: 3
})

// ❌ 不推荐
const columns = useResponsiveValue({
  xs: 1,
  md: 2,
  lg: 3
})
</script>
```

### 4. 合理使用响应式指令

```vue
<template>
  <!-- ✅ 推荐 - 简单的显示/隐藏 -->
  <div v-responsive:show="{ mobile: false, desktop: true }">
    桌面端菜单
  </div>
  
  <!-- ❌ 不推荐 - 复杂的样式应该用 CSS -->
  <div v-responsive:style="complexStyleObject">
    复杂样式
  </div>
</template>
```

## 测试和调试

### 响应式测试页面

访问 `/responsive-test` 页面（仅开发环境）来测试响应式功能。

### 浏览器开发者工具

1. 打开浏览器开发者工具
2. 切换到设备模拟模式
3. 测试不同的屏幕尺寸
4. 观察断点指示器的变化

### 断点调试

```javascript
// 在控制台中查看当前断点信息
console.log('当前断点:', responsiveManager.getCurrentBreakpoint())
console.log('窗口宽度:', window.innerWidth)
```

## 常见问题

### Q: 为什么我的响应式样式没有生效？

A: 检查以下几点：
1. 确保已导入响应式混入文件
2. 检查断点值是否正确
3. 确保 CSS 选择器优先级正确
4. 检查是否有其他样式覆盖

### Q: 如何自定义断点？

A: 修改 `src/constants/responsive.js` 中的 `BREAKPOINTS` 配置，并更新相关的混入文件。

### Q: 响应式指令性能如何？

A: 响应式指令使用了高效的事件监听机制，只在断点变化时才重新计算，性能影响很小。

## 更新日志

- v1.0.0: 初始版本，包含基础响应式功能
- v1.1.0: 添加响应式指令和组件
- v1.2.0: 优化性能，添加工具函数
- v1.3.0: 完善文档和测试页面
