/*
 * @Author: <PERSON>
 * @Date: 2024-04-02 17:52:04
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-06-25 10:00:00
 */

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    // 统一的响应式断点配置
    screens: {
      'xs': '480px',    // 超小屏幕 (手机横屏)
      'sm': '640px',    // 小屏幕 (平板竖屏)
      'md': '768px',    // 中等屏幕 (平板横屏)
      'lg': '992px',    // 大屏幕 (桌面)
      'xl': '1200px',   // 超大屏幕 (大桌面)
      '2xl': '1320px',  // 超超大屏幕 (超大桌面)
      '3xl': '1536px',  // 极大屏幕
      // 自定义断点
      'mobile': {'max': '991px'},     // 移动端 (小于992px)
      'tablet': {'min': '768px', 'max': '1199px'},  // 平板端
      'desktop': {'min': '1200px'},   // 桌面端
      // 特殊断点
      'mobile-sm': {'max': '639px'},  // 小手机
      'mobile-lg': {'min': '640px', 'max': '991px'},  // 大手机/小平板
    },
    extend: {
      // 扩展容器配置
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',
          'sm': '2rem',
          'lg': '4rem',
          'xl': '5rem',
          '2xl': '6rem',
        },
        screens: {
          'sm': '640px',
          'md': '768px',
          'lg': '992px',
          'xl': '1200px',
          '2xl': '1320px',
        }
      },
      // 扩展间距
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // 扩展字体大小
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      // 扩展最大宽度
      maxWidth: {
        'xs': '20rem',
        'sm': '24rem',
        'md': '28rem',
        'lg': '32rem',
        'xl': '36rem',
        '2xl': '42rem',
        '3xl': '48rem',
        '4xl': '56rem',
        '5xl': '64rem',
        '6xl': '72rem',
        '7xl': '80rem',
        'full': '100%',
        'screen-sm': '640px',
        'screen-md': '768px',
        'screen-lg': '992px',
        'screen-xl': '1200px',
        'screen-2xl': '1320px',
      }
    }
  },
  plugins: [
    // 添加响应式工具插件
    function({ addUtilities, theme }) {
      const newUtilities = {
        // 响应式显示工具类
        '.show-mobile': {
          '@media (min-width: 992px)': {
            display: 'none !important',
          },
        },
        '.hide-mobile': {
          '@media (max-width: 991px)': {
            display: 'none !important',
          },
        },
        '.show-tablet': {
          '@media (max-width: 767px)': {
            display: 'none !important',
          },
          '@media (min-width: 1200px)': {
            display: 'none !important',
          },
        },
        '.show-desktop': {
          '@media (max-width: 1199px)': {
            display: 'none !important',
          },
        },
        // 响应式文本对齐
        '.text-center-mobile': {
          '@media (max-width: 991px)': {
            'text-align': 'center',
          },
        },
        '.text-left-desktop': {
          '@media (min-width: 992px)': {
            'text-align': 'left',
          },
        },
        // 响应式内边距
        '.px-responsive': {
          'padding-left': '1rem',
          'padding-right': '1rem',
          '@media (min-width: 768px)': {
            'padding-left': '2rem',
            'padding-right': '2rem',
          },
          '@media (min-width: 1200px)': {
            'padding-left': '4rem',
            'padding-right': '4rem',
          },
        },
        // 响应式容器
        '.container-responsive': {
          'width': '100%',
          'margin-left': 'auto',
          'margin-right': 'auto',
          'padding-left': '1rem',
          'padding-right': '1rem',
          '@media (min-width: 640px)': {
            'max-width': '640px',
            'padding-left': '2rem',
            'padding-right': '2rem',
          },
          '@media (min-width: 768px)': {
            'max-width': '768px',
          },
          '@media (min-width: 992px)': {
            'max-width': '992px',
          },
          '@media (min-width: 1200px)': {
            'max-width': '1200px',
          },
          '@media (min-width: 1320px)': {
            'max-width': '1320px',
          },
        }
      }
      addUtilities(newUtilities)
    }
  ]
}
