/*
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-05 14:27:04
 */
import { ref, watch } from "vue"

import { i18n } from "../i18n/index.js"

/** 项目标题 */
const VITE_APP_TITLE = import.meta.env.VITE_APP_TITLE ?? "website"

/** 动态标题 */
const dynamicTitle = ref("")

/** 设置标题 */
const setTitle = (title) => {
  dynamicTitle.value = title ? `${VITE_APP_TITLE} | ${i18n.global.t(title)}` : VITE_APP_TITLE
}

/** 监听标题变化 */
watch(dynamicTitle, (value, oldValue) => {
  if (document && value !== oldValue) {
    document.title = value
  }
})

export function useTitle() {
  return { setTitle }
}
