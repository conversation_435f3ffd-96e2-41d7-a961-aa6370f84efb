<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-13 11:30:05
-->

<template>
  <div class="great-health-main">
    <div class="content">
      <div class="video-view">
        <div class="txt-main">
          <div class="txt">{{ langMap[lang].desc1 }}</div>
          <div class="txt">{{ langMap[lang].desc2 }}</div>
          <div class="txt-tip">{{ langMap[lang].desc3 }}</div>
        </div>
        <video class="video" :src="greatHealthVideo" controls controlsList="nodownload" preload="metadata">
          您的浏览器不支持视频标签。
        </video>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue"

import { i18n } from "@/i18n"

const lang = ref(i18n.global.locale)

const greatHealthVideo = ref("/resources/video/greatHealthVideo.mp4")

const langMap = ref({
  zh: {
    desc1: "中兆国际集团",
    desc2: "大健康板块隆重上市",
    desc3: "各界大咖明星齐祝福、共庆辉煌时刻"
  },
  en: {
    desc1: "Zhongzhao International Group",
    desc2: "The Comprehensive Health Segment is Officially Launched",
    desc3: "Wishes from Celebrities to Mark this Moment"
  },
  zh_hk: {
    desc1: "中兆國際集團",
    desc2: "大健康板塊隆重上市",
    desc3: "各界大咖明星齊祝福、共慶輝煌時刻"
  }
})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)
</script>

<style lang="scss" scoped>
.great-health-main {
  background: #ffffff;

  .content {
    background: url("@/assets/home/<USER>") no-repeat center center;
    background-size: cover;

    display: flex;
    justify-content: center;

    .video-view {
      width: 80%;

      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-top: 100px;
      padding-bottom: 250px;

      .txt-main {
        width: 100%;
        text-align: center;
        margin-bottom: 50px;

        .txt {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-size: 40px;
          color: #ffffff;
          letter-spacing: 3px;
          z-index: 1;
          font-weight: 600;
          text-shadow: 1px 1px 10px rgba(#ffffff, 0.1);
        }

        .txt-tip {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 500;
          font-size: 28px;
          color: #90d6ff;
          line-height: 50px;
          letter-spacing: 2px;
          text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          margin-top: 10px;
        }
      }

      .video {
        width: 800px;
        height: 100%;
      }
    }
  }
}

@media (max-width: 1320px) {
  .great-health-main .content .video-view {
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
  }

  .great-health-main .content .video-view .video {
    width: 80%;
    height: 100%;
  }
}
@media (max-width: 1024px) {
  .great-health-main .content .video-view .txt-main .txt {
    font-size: 30px;
  }
  .great-health-main .content .video-view .txt-main .txt-tip {
    font-size: 20px;
    line-height: 40px;
  }
}
@media (max-width: 820px) {
}
@media (max-width: 768px) {
  .great-health-main .content .video-view .txt-main .txt {
    font-size: 28px;
  }
  .great-health-main .content .video-view .txt-main .txt-tip {
    font-size: 18px;
    line-height: 25px;
  }

  .great-health-main .content .video-view .video {
    width: 100%;
    height: 100%;
  }
}
@media (max-width: 576px) {
  .great-health-main .content .video-view .txt-main .txt {
    font-size: 24px;
  }
  .great-health-main .content .video-view .txt-main .txt-tip {
    font-size: 16px;
    line-height: 22px;
  }
}
@media (max-width: 480px) {
}
@media (max-width: 390px) {
}
@media (max-width: 380px) {
}
</style>
