/**
 * 响应式 SCSS 混入
 * 提供统一的响应式断点和常用的响应式样式混入
 */

// 导入响应式常量 (需要在 CSS 中定义对应的变量)
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1320px;
  --breakpoint-xxxl: 1536px;
  
  --mobile-max: 991px;
  --tablet-min: 768px;
  --tablet-max: 1199px;
  --desktop-min: 1200px;
}

// 响应式断点混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (min-width: 480px) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (min-width: 640px) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (min-width: 768px) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (min-width: 992px) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (min-width: 1200px) {
      @content;
    }
  }
  @else if $breakpoint == xxl {
    @media (min-width: 1320px) {
      @content;
    }
  }
  @else if $breakpoint == xxxl {
    @media (min-width: 1536px) {
      @content;
    }
  }
}

// 最大宽度响应式混入
@mixin respond-to-max($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: 479px) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (max-width: 639px) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (max-width: 767px) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (max-width: 991px) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (max-width: 1199px) {
      @content;
    }
  }
  @else if $breakpoint == xxl {
    @media (max-width: 1319px) {
      @content;
    }
  }
  @else if $breakpoint == xxxl {
    @media (max-width: 1535px) {
      @content;
    }
  }
}

// 设备类型响应式混入
@mixin mobile-only {
  @media (max-width: 991px) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: 768px) and (max-width: 1199px) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: 1200px) {
    @content;
  }
}

@mixin mobile-and-tablet {
  @media (max-width: 1199px) {
    @content;
  }
}

@mixin tablet-and-desktop {
  @media (min-width: 768px) {
    @content;
  }
}

// 范围响应式混入
@mixin respond-between($min, $max) {
  @media (min-width: #{$min}px) and (max-width: #{$max}px) {
    @content;
  }
}

// 高分辨率屏幕混入
@mixin retina {
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    @content;
  }
}

// 响应式字体大小混入
@mixin responsive-font-size($mobile, $tablet: null, $desktop: null) {
  font-size: $mobile;
  
  @if $tablet {
    @include tablet-and-desktop {
      font-size: $tablet;
    }
  }
  
  @if $desktop {
    @include desktop-only {
      font-size: $desktop;
    }
  }
}

// 响应式间距混入
@mixin responsive-spacing($property, $mobile, $tablet: null, $desktop: null) {
  #{$property}: $mobile;
  
  @if $tablet {
    @include tablet-and-desktop {
      #{$property}: $tablet;
    }
  }
  
  @if $desktop {
    @include desktop-only {
      #{$property}: $desktop;
    }
  }
}

// 响应式容器混入
@mixin responsive-container($max-width: true, $padding: true) {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  
  @if $padding {
    padding-left: 1rem;
    padding-right: 1rem;
    
    @include respond-to(md) {
      padding-left: 2rem;
      padding-right: 2rem;
    }
    
    @include respond-to(xl) {
      padding-left: 4rem;
      padding-right: 4rem;
    }
  }
  
  @if $max-width {
    @include respond-to(sm) {
      max-width: 640px;
    }
    
    @include respond-to(md) {
      max-width: 768px;
    }
    
    @include respond-to(lg) {
      max-width: 992px;
    }
    
    @include respond-to(xl) {
      max-width: 1200px;
    }
    
    @include respond-to(xxl) {
      max-width: 1320px;
    }
  }
}

// 响应式网格混入
@mixin responsive-grid($mobile-cols: 1, $tablet-cols: 2, $desktop-cols: 3, $gap: 1rem) {
  display: grid;
  gap: $gap;
  grid-template-columns: repeat($mobile-cols, 1fr);
  
  @include tablet-and-desktop {
    grid-template-columns: repeat($tablet-cols, 1fr);
  }
  
  @include desktop-only {
    grid-template-columns: repeat($desktop-cols, 1fr);
  }
}

// 响应式 Flexbox 混入
@mixin responsive-flex($mobile-direction: column, $tablet-direction: row, $desktop-direction: null) {
  display: flex;
  flex-direction: $mobile-direction;
  
  @include tablet-and-desktop {
    flex-direction: $tablet-direction;
  }
  
  @if $desktop-direction {
    @include desktop-only {
      flex-direction: $desktop-direction;
    }
  }
}

// 响应式显示/隐藏混入
@mixin show-on-mobile {
  display: block;
  
  @include tablet-and-desktop {
    display: none;
  }
}

@mixin hide-on-mobile {
  display: none;
  
  @include tablet-and-desktop {
    display: block;
  }
}

@mixin show-on-tablet {
  display: none;
  
  @include tablet-only {
    display: block;
  }
}

@mixin show-on-desktop {
  display: none;
  
  @include desktop-only {
    display: block;
  }
}

// 响应式图片混入
@mixin responsive-image {
  max-width: 100%;
  height: auto;
  display: block;
}

// 响应式视频混入
@mixin responsive-video {
  position: relative;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  height: 0;
  overflow: hidden;
  
  iframe,
  object,
  embed,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 响应式表格混入
@mixin responsive-table {
  width: 100%;
  overflow-x: auto;
  
  table {
    min-width: 600px;
    width: 100%;
  }
  
  @include mobile-only {
    table {
      font-size: 0.875rem;
    }
    
    th,
    td {
      padding: 0.5rem;
    }
  }
}

// 响应式卡片混入
@mixin responsive-card($mobile-padding: 1rem, $desktop-padding: 2rem) {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  padding: $mobile-padding;
  
  @include desktop-only {
    padding: $desktop-padding;
  }
}

// 响应式按钮混入
@mixin responsive-button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  
  @include mobile-only {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    width: 100%;
  }
}

// 响应式导航混入
@mixin responsive-nav {
  @include mobile-only {
    flex-direction: column;
    
    .nav-item {
      width: 100%;
      text-align: center;
      border-bottom: 1px solid var(--el-border-color);
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
