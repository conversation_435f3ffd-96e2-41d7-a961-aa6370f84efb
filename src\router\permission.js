/*
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-06-05 21:10:19
 */
import router from "@/router"
import { useUserStoreHook } from "@/store/modules/user"
import { usePermissionStoreHook } from "@/store/modules/permission"
import { ElMessage } from "element-plus"
import { setRouteChange } from "@/hooks/useRouteListener"
import { useTitle } from "@/hooks/useTitle"
import { getToken } from "@/utils/cache/cookies"
import routeSettings from "@/config/route"
import isWhiteList from "@/config/white-list"
import NProgress from "nprogress"
import "nprogress/nprogress.css"

import { useAppStore } from "@/store/modules/app"

const { setTitle } = useTitle()
NProgress.configure({ showSpinner: false })

router.beforeEach(async (to, _from, next) => {
  NProgress.start()
  // const userStore = useUserStoreHook()
  // const permissionStore = usePermissionStoreHook()
  // const token = getToken()

  if (to.path === "/login") {
    return next("/")
  }

  next()
})

router.afterEach((to) => {
  const appStore = useAppStore()

  setTitle(to.meta.title)
  document.querySelector(".app-scrollbar")?.scrollTo(0, 0)
  // document.body.scrollTo(0, 0)
  window.scrollTo(0, 0)
  appStore.closeSidebar()
  NProgress.done()
})
