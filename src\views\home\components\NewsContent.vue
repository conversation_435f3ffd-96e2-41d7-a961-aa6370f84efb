<template>
  <div class="news-content">
    <h2 class="news-title">{{ $t("news.title") }}</h2>
    <div class="news-list">
      <div class="item" v-for="item in newsListData" :key="item.id" @click="goDetails(item.id)">
        <div class="item-img">
          <el-image class="img" :src="item.newBgImg" alt="" fit="cover" />
        </div>
        <div class="item-content">
          <div class="item-title" v-if="refreshNews">{{ getItemTitle(item) }}</div>
          <div class="item-time">
            <span>{{ getNewTime(item.newTime) }}</span>
            <span class="icon"
              ><el-icon><Right /></el-icon
            ></span>
          </div>
        </div>
      </div>
    </div>
    <div class="more-btn" v-if="hasNewsData">
      <div class="btn" @click="getMoreNews">{{ $t("public.loadingMore") }}</div>
    </div>
  </div>
</template>
<script setup>
import { nextTick, onMounted, reactive, ref, watch } from "vue"

import { useRouter } from "vue-router"
import { i18n } from "@/i18n"

import dayjs from "dayjs"
import { newsList } from "@/api/new/index.js"

const refreshNews = ref(true)

const router = useRouter()
const lang = ref(i18n.global.locale)

const page = ref(1)
const pageSize = ref(9)
const loading = ref(false)
const hasNewsData = ref(true)
const newsListData = ref([])

const getNewsList = () => {
  if (loading.value) return

  loading.value = true

  const params = {
    page: page.value,
    pageSize: pageSize.value
  }
  newsList(params)
    .then((res) => {
      if (res.code == 200) {
        //根据 sort 排序
        res.data.sort((a, b) => a.sort - b.sort)
        newsListData.value = [...newsListData.value, ...res.data]

        if (res.data.length < pageSize.value) {
          hasNewsData.value = false
        }

        nextTick(() => {
          refreshNewsList()
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}
getNewsList()

const getMoreNews = () => {
  page.value++
  getNewsList()
}

const getItemTitle = (item) => {
  const selectMap = {
    zh: item.titleZh,
    zh_hk: item.titleHk,
    en: item.titleEn
  }
  return selectMap[lang.value]
}

const getNewTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD")
}

const goDetails = (id) => {
  router.push({ name: "NewsDetails", query: { id } })
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"

    refreshNews.value = false
    nextTick(() => {
      refreshNews.value = true

      nextTick(() => {
        refreshNewsList()
      })
    })
  }
)

const refreshNewsList = () => {
  const itemTitle = document.querySelectorAll(".item-title")
  let maxHeight = 0
  itemTitle.forEach((item) => {
    maxHeight = Math.max(maxHeight, item.clientHeight)
  })
  itemTitle.forEach((item) => {
    item.style.height = `${maxHeight}px`
  })
}
</script>

<style lang="scss" scoped>
.news-content {
  padding-top: 130px;
  padding-bottom: 150px;

  .news-title {
    font-size: 50px;
    color: rgba(0, 0, 0, 0.85);
    letter-spacing: 5px;
    text-align: center;
  }

  .news-list {
    width: 1320px;
    max-width: 100%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding-top: 50px;
    padding-bottom: 60px;
  }

  .more-btn {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    .btn {
      font-size: 20px;
      color: #fff;
      background: #1c2a77;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
    }
  }

  .item {
    flex: 33.33333%;
    background: #ffffff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transition: all 0.3s;
    overflow: hidden;

    &:hover {
      box-shadow: 2px 2px 8px 6px rgba(0, 0, 0, 0.11);
      transform: translateY(-10px);
      cursor: pointer;
    }

    .item-img {
      height: 250px;
      .img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .item-content {
      padding: 20px 22px 15px 22px;
      .item-title {
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        // letter-spacing: 1.5px;
        font-weight: 700;
        width: 100%;
      }
    }

    .item-text {
      font-size: 14px;
      color: #6d7278;
      // letter-spacing: 1px;
      text-align: left;
      display: -webkit-box;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      box-sizing: border-box;
      padding-top: 11px;
    }

    .item-time {
      font-size: 14px;
      color: #6d7278;
      // letter-spacing: 1px;
      box-sizing: border-box;
      padding-top: 10px;
      border-top: 1px solid #e5e5e5;
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

@import "./media.scss";
</style>
