// 全局 CSS 变量
@import "./variables.css";
// 响应式混入 (需要在其他样式之前导入)
@import "./mixins-responsive.scss";
// Transition
@import "./transition.scss";
// Element Plus
@import "./element-plus.css";
@import "./element-plus.scss";

// 注册多主题
@import "./theme/register.scss";
// Mixins
@import "./mixins.scss";
// View Transition
@import "./view-transition.scss";

// Theme 自定义的
@import "./mixins-theme-normal.scss";
@import "./mixins-theme-dark.scss";
@import "./mixins-theme-dark-blue.scss";

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 20px;
}

html {
  height: 100%;
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  font-display: block;
  @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

// 修改进度条的样式
#nprogress {
  .bar {
    background: #efc8a5;
  }
}

// 主题中需要自定义的样式
html.normal {
  @extend %theme-normal-custom;
}
html.dark {
  @extend %theme-dark-custom;
}
html.dark-blue {
  @extend %theme-dark-blue-custom;
}
