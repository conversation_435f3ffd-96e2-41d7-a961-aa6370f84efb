import { createRouter } from "vue-router"
import { history, flatMultiLevelRoutes } from "./helper"
import routeSettings from "@/config/route"

const Layouts = () => import("@/layouts/index.vue")

/**
 * 常驻路由
 * 除了 redirect/403/404/login 等隐藏页面，其他页面建议设置 Name 属性
 *
 * 设置该路由在侧边栏和面包屑中展示的名字
 * title?: string
 *
 * 设置该路由的图标，记得将 svg 导入 @/icons/svg
 * svgIcon?: string
 *
 * 设置该路由的图标，直接使用 Element Plus 的 Icon（与 svgIcon 同时设置时，svgIcon 将优先生效）
 * elIcon?: string
 * 
 * 默认 false，设置 true 的时候该路由不会在侧边栏出现
 * hidden?: boolean
 * 
 * 设置能进入该路由的角色，支持多个角色叠加
 *roles?: string[]

 * 默认 true，如果设置为 false，则不会在面包屑中显示
 * breadcrumb?: boolean
 * 
 * 默认 false，如果设置为 true，它则会固定在 tags-view 中
 * affix?: boolean
 * 
 * 当一个路由下面的 children 声明的路由大于 1 个时，自动会变成嵌套的模式，
 * 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 * 若想不管路由下面的 children 声明的个数都显示你的根路由，
 * 
 * 可以设置 alwaysShow: true，这样就会忽略之前定义的规则，一直显示根路由
 * alwaysShow?: boolean
 * 示例: activeMenu: "/xxx/xxx"，
 * 当设置了该属性进入路由时，则会高亮 activeMenu 属性对应的侧边栏。
 * 该属性适合使用在有 hidden: true 属性的路由上
 * activeMenu?: string
 * 
 * 是否缓存该路由页面
 * 默认为 false，为 true 时代表需要缓存，此时该路由和该页面都需要设置一致的 Name
 * keepAlive?: boolean
 */
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layouts,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/views/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/views/error-page/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    meta: {
      hidden: true
    },
    alias: "/:pathMatch(.*)*"
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/",
    component: Layouts,
    redirect: "/",
    children: [
      {
        path: "",
        component: () => import("@/views/home/<USER>"),
        name: "Home",
        meta: {
          title: "menus.home",
          affix: true
        }
      },
      {
        path: "great-health",
        component: () => import("@/views/home/<USER>/great-health.vue"),
        name: "GreatHealth",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/"
        }
      }
    ]
  },
  {
    path: "/about",
    component: Layouts,
    children: [
      {
        path: "",
        component: () => import("@/views/about/index.vue"),
        name: "About",
        meta: {
          title: "menus.aboutUs",
          affix: true
        }
      }
    ]
  },
  {
    path: "/business-segment",
    component: Layouts,
    children: [
      {
        path: "",
        component: () => import("@/views/business-segment/index.vue"),
        name: "BusinessSegment",
        meta: {
          title: "menus.businessSegment",
          affix: true
        }
      },
      {
        path: "business-details",
        component: () => import("@/views/business-segment/business-details.vue"),
        name: "BusinessDetails",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      },
      {
        path: "ecology-sustainable-development",
        component: () => import("@/views/business-segment/ecology-sustainable-development.vue"),
        name: "EcologySustainableDevelopment",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      },
      {
        path: "earlyCancerScreening",
        component: () => import("@/views/business-segment/earlyCancerScreening.vue"),
        name: "EarlyCancerScreening",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      },
      {
        path: "geneTherapy",
        component: () => import("@/views/business-segment/geneTherapy.vue"),
        name: "EeneTherapy",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      },
      {
        path: "industrial-base-details",
        component: () => import("@/views/business-segment/industrial-base-details.vue"),
        name: "IndustrialBaseDetails",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      },
      {
        path: "energy-valley-details",
        component: () => import("@/views/business-segment/energy-valley-details.vue"),
        name: "EnergyValleyDetails",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      },
      {
        path: "science-academy-details",
        component: () => import("@/views/business-segment/components/science-academy-details.vue"),
        name: "ScienceAcademyDetails",
        meta: {
          title: "",
          affix: true,
          hidden: true,
          activeMenu: "/business-segment"
        }
      }
    ]
  },
  // {
  //   path: "/leadership-team",
  //   component: Layouts,
  //   children: [
  //     {
  //       path: "",
  //       component: () => import("@/views/leadership-team/index.vue"),
  //       name: "LeadershipTeam",
  //       meta: {
  //         title: "menus.leadershipTeam",
  //         affix: true
  //       }
  //     }
  //   ]
  // },
  {
    path: "/tangka",
    component: Layouts,
    children: [
      {
        path: "",
        component: () => import("@/views/tangka/index.vue"),
        name: "Tangka",
        meta: {
          title: "menus.tangKa",
          affix: true
        }
      },
      {
        path: "tangka-details",
        component: () => import("@/views/tangka/tangka-details.vue"),
        name: "TangkaDetails",
        meta: {
          title: "workDetails.title2",
          hidden: true,
          activeMenu: "/tangka"
        }
      }
    ]
  },
  {
    path: "/news",
    component: Layouts,
    children: [
      {
        path: "",
        component: () => import("@/views/news/index.vue"),
        name: "News",
        meta: {
          title: "menus.news",
          affix: true
        }
      },
      {
        path: "news-details",
        component: () => import("@/views/news/news-details.vue"),
        name: "NewsDetails",
        meta: {
          title: "menus.news",
          affix: true,
          hidden: true,
          activeMenu: "/news"
        }
      }
    ]
  },
  {
    path: "/contact-us",
    component: Layouts,
    children: [
      {
        path: "",
        component: () => import("@/views/contact-us/index.vue"),
        name: "ContactUs",
        meta: {
          title: "menus.contactUs",
          affix: true
        }
      }
    ]
  }
]

/**
 * 动态路由
 * 用来放置有权限 (Roles 属性) 的路由
 * 必须带有 Name 属性
 */
export const dynamicRoutes = []

const router = createRouter({
  history,
  routes: routeSettings.thirdLevelRouteCache ? flatMultiLevelRoutes(constantRoutes) : constantRoutes
})

/** 重置路由 */
export function resetRouter() {
  // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
  try {
    router.getRoutes().forEach((route) => {
      const { name, meta } = route
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    window.location.reload()
  }
}

export default router
