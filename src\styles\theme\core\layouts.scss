/** Layout 相关 */

.app-wrapper {
  // 侧边栏
  .sidebar-container {
    background-color: var(--el-bg-color);
    .el-menu {
      background-color: var(--el-bg-color);
      .el-menu-item {
        background-color: var(--el-bg-color);
        &.is-active,
        &:hover {
          background-color: var(--el-bg-color-overlay);
          color: #ffffff;
        }
      }
    }
    .el-sub-menu__title {
      background-color: var(--el-bg-color);
    }
    .el-sub-menu {
      &.is-active {
        > .el-sub-menu__title {
          color: #ffffff !important;
        }
      }
    }
  }
}

// 右侧设置面板
.handle-button {
  background-color: lighten($theme-bg-color, 20%) !important;
}
