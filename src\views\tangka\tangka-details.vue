<template>
  <div class="tangka-details">
    <div class="tangka-details-main">
      <div class="left" ref="imgViewRef">
        <div class="zoom-main" v-loading="loading" element-loading-background="#000000">
          <v3-drag-zoom-container
            class="drag-zoom-container"
            ref="dragZoomContainer"
            maxZoom="5"
            minZoom="0.2"
            align="auto"
          >
            <el-image @load="loadImage" :src="activeImg" fit="cover" />
            <!-- <img @load="loadImage" class="draggable-image" :src="activeImg" fit="cover" ref="draggableImageRef" /> -->
          </v3-drag-zoom-container>
        </div>

        <div class="handle-list">
          <div class="item" @click.stop="resetContainer">
            <el-icon><Refresh /></el-icon>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="btn-view">
          <div class="btn" @click="showTxtDetailsEvent">
            <el-icon v-if="showTxtDetails"><ArrowRightBold /></el-icon>
            <el-icon v-else><ArrowLeftBold /></el-icon>
          </div>
        </div>
        <div class="content" :class="{ 'content-show': showTxtDetails }">
          <div class="content-main">
            <div class="top-tips">
              <span @click="gotoTangkaIndex" class="first">{{ $t("workDetails.title1") }}</span
              >/ <span class="second">{{ $t("workDetails.title2") }}</span>
            </div>

            <h3 class="name">{{ workInfo[lang].title }}</h3>
            <div class="detail-box">
              <h2 class="detail-docs">{{ $t("workDetails.imgDetail") }}</h2>
              <div class="detail-list">
                <div class="detail-img" @click="changeImg(item)" v-for="(item, index) in worksDetailList" :key="index">
                  <img :src="item.zipImg" alt="" class="img-item" />
                </div>
              </div>
            </div>

            <div class="view-num">
              <el-icon><View /></el-icon>
              <p>
                128 <span> {{ $t("workDetails.look") }}</span>
              </p>
            </div>
            <div class="desc-view">
              <!-- <p>{{ workInfo[lang].desc }}</p> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, reactive, onBeforeMount } from "vue"
import { useRouter } from "vue-router"
import { V3DragZoomContainer } from "v3-drag-zoom"
import worksList from "./works-list"

import { i18n } from "@/i18n"
import { watch } from "vue"
import { useDevice } from "@/hooks/useDevice"

const { isMobile } = useDevice()
const activeImg = ref(null)
let workInfo = reactive({})
const router = useRouter()

const worksDetailList = ref([])
const workId = ref(0)
const lang = ref(i18n.global.locale)
const showTxtDetails = ref(true)

const loading = ref(true)

const loadImage = () => {
  loading.value = false

  resetContainer()
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

watch(
  () => isMobile.value,
  (newVal) => {
    newVal ? (showTxtDetails.value = false) : (showTxtDetails.value = true)
  },
  { immediate: true }
)

const dragZoomContainer = ref(null)

// 重置
const resetContainer = () => {
  nextTick(() => {
    if (dragZoomContainer.value) {
      dragZoomContainer.value.reset()
    }
  })
}

const showTxtDetailsEvent = () => {
  showTxtDetails.value = !showTxtDetails.value
}

const gotoTangkaIndex = () => {
  localStorage.setItem("tangkaPart", "tangkaPart6")

  router.push({ path: "/tangka" })
}

const changeImg = (info) => {
  activeImg.value = info.img
  loading.value = true

  if (isMobile.value) {
    showTxtDetails.value = false
  }
}

onBeforeMount(() => {
  workId.value = router.currentRoute.value.query.id
  workInfo = worksList.find((item) => item.id == workId.value)
  activeImg.value = workInfo.img

  worksDetailList.value = workInfo.detailImg
})

onMounted(() => {})
</script>

<style lang="scss" scoped>
.tangka-details {
  background: #000000;
  .tangka-details-main {
    display: flex;
    height: 895px;

    :deep(.el-loading-spinner .path) {
      stroke: #ffffff;
    }

    .left {
      flex: 1;
      background: #292929;
      position: relative;

      .zoom-main {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        .drag-zoom-container {
          width: 100%;
          height: 100%;

          .draggable-image {
            width: 80%;
            height: auto;
            object-fit: cover;

            margin: 0 auto;
          }
        }
      }

      .handle-list {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;
        position: absolute;
        bottom: 42px;
        right: 80px;

        .item {
          width: 44px;
          height: 44px;
          background: #fff;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: rgba(0, 0, 0, 0.4);
          cursor: pointer;
          color: #ffffff;
          font-size: 27px;

          position: relative;
          z-index: 9999;

          &:hover {
            background: rgba(0, 0, 0, 1);
          }
        }
      }
    }

    .right {
      .econtainer {
        max-width: 1000px;
        width: 100%;
        margin: 0 auto;

        :deep(.el-breadcrumb) {
          font-size: 18px;

          &.el-breadcrumb__inner a:hover,
          .el-breadcrumb__inner.is-link:hover {
            color: inherit;
          }
        }
      }

      :deep(.el-breadcrumb__inner is-link) {
        color: #ffffff;
      }

      display: flex;

      position: relative;
      z-index: 999;
      background: #000000;

      .btn-view {
        width: 80px;
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 27px;
      }
      .btn {
        height: 40px;
        width: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 27px;
        color: #ffffff;
        cursor: pointer;
      }

      .content {
        width: 0px;
        padding: 80px 0;
        transition: width 0.5s;

        .content-main {
          width: 505px;
          padding-right: 30px;
        }

        .top-tips {
          color: #ffffff;
          font-size: 16px;
          margin-bottom: 70px;

          .first {
            padding-right: 12px;

            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }

          .second {
            padding-left: 10px;
          }
        }

        .name {
          color: #ffffff;
          font-size: 32px;
          margin-bottom: 20px;
        }

        .detail-box {
          margin-bottom: 20px;

          .detail-list {
            width: calc(100% - 0px);
            height: 170px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;

            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            &::-webkit-scrollbar-track {
              background: #292929;
            }
            &::-webkit-scrollbar-thumb {
              background: #ffffff;
              border-radius: 6px;
            }
          }

          .detail-img {
            display: inline-block;
            width: 150px;
            height: 150px;
            margin-right: 10px;
            cursor: pointer;
          }

          .img-item {
            width: 100%;
            height: 100%;
          }
        }

        .detail-docs {
          font-size: 20px;
          padding-bottom: 10px;
          color: #ffffff;
        }

        .view-num {
          display: flex;
          align-items: center;
          gap: 10px;
          color: #ffffff;
          font-size: 16px;
          margin-bottom: 40px;
        }

        .desc-view {
          color: #ffffff;
          font-size: 16px;
          line-height: 24px;
        }
      }

      .content-show {
        width: 505px;
      }
    }
  }
}

@media (max-width: 1320px) {
}

@media (max-width: 1200px) {
}

@media (max-width: 1024px) {
  .tangka-details .tangka-details-main {
    height: 750px;
  }

  .tangka-details .tangka-details-main .right .content .name {
    font-size: 28px;
  }
}

@media (max-width: 820px) {
}

@media (max-width: 768px) {
  .tangka-details .tangka-details-main {
    height: 600px;
  }

  .tangka-details .tangka-details-main .left .handle-list {
    right: 20px;
    bottom: 20px;
  }
}

@media (max-width: 576px) {
  .tangka-details .tangka-details-main {
    height: 450px;
  }

  .tangka-details .tangka-details-main .right .content .content-main {
    padding-right: 100px;
  }

  .tangka-details .tangka-details-main .right .content {
    padding-top: 40px;
    padding-bottom: 40px;
  }

  .tangka-details .tangka-details-main .right .content .view-num {
    margin-top: 40px;
  }

  .tangka-details .tangka-details-main .right .content .name {
    font-size: 22px;
  }

  .tangka-details .tangka-details-main .right .content .detail-docs {
    font-size: 16px;
  }

  .tangka-details .tangka-details-main .right .content .top-tips {
    margin-bottom: 30px;
  }
}
@media (max-width: 480px) {
  .tangka-details .tangka-details-main .left .handle-list {
    right: 20px;
    bottom: 20px;
  }

  .tangka-details .tangka-details-main .right .content .content-main {
    padding-right: 200px;
  }

  .tangka-details .tangka-details-main .right .content .name {
    font-size: 24px;
  }
}
@media (max-width: 390px) {
  .tangka-details .tangka-details-main .right .content .content-main {
    padding-right: 230px;
  }

  .tangka-details .tangka-details-main .right .content .name {
    font-size: 22px;
  }
}
@media (max-width: 380px) {
}
</style>
