<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-19 16:24:39
-->

<template>
  <div class="leadership-team-main">
    <div class="background-img">
      <p class="bg-desc">{{ $t("leadershipTeam.title") }}</p>
    </div>

    <div class="content">
      <div class="sticky-header">
        <div class="card">
          <div class="title">
            <div :class="{ active: activeTeamDom == 'part1' }" @click="gotoDomView('part1')">
              {{ $t("leadershipTeam.subTitle1") }}
            </div>
            <div :class="{ active: activeTeamDom == 'part2' }" @click="gotoDomView('part2')">
              {{ $t("leadershipTeam.subTitle2") }}
            </div>
          </div>
        </div>
      </div>

      <div class="cards-view">
        <div class="cards-main">
          <div class="cards-part1" id="part1">
            <div class="tip-title">
              <h3>{{ $t("leadershipTeam.subTitle1") }}</h3>
              <h4>BOARD OF DIRECTORS</h4>
            </div>
            <div class="info-main">
              <div class="info-view">
                <div class="img-view">
                  <el-image class="img" :src="teamImg1" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.presidentName") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.president_desc") }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="cards-part2" id="part2">
            <div class="tip-title">
              <h3>{{ $t("leadershipTeam.subTitle2") }}</h3>
              <h4>TEAM OF SCIENTISTS</h4>
            </div>
            <div class="member-list">
              <div class="member-item">
                <div class="img-view">
                  <el-image class="img" :src="teamImg2" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.scientistLiu") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistLiu_desc1") }}</div>
                  <div class="honor-list">
                    <div class="honor-item">{{ $t("public.scientist_honor1") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLiu_honor2") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLiu_honor3") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLiu_honor4") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLiu_honor5") }}</div>
                    <div class="honor-item">
                      {{ $t("leadershipTeam.scientistLiu_honor6") }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="member-item">
                <div class="img-view">
                  <el-image class="img" :src="teamImg3" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.scientistWang") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistWang_desc1") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistWang_desc2") }}</div>
                  <div class="honor-list">
                    <div class="honor-item">{{ $t("public.scientist_honor1") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistWang_honor2") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistWang_honor3") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistWang_honor4") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistWang_honor5") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistWang_honor6") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistWang_honor7") }}</div>
                  </div>
                </div>
              </div>

              <div class="member-item">
                <div class="img-view">
                  <el-image class="img" :src="teamImg4" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.scientistZhao") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistZhao_desc1") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistZhao_desc2") }}</div>
                  <div class="honor-list">
                    <div class="honor-item">{{ $t("public.scientist_honor1") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhao_honor2") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhao_honor3") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhao_honor4") }}</div>
                    <div class="honor-item">
                      {{ $t("leadershipTeam.scientistZhao_honor5") }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="member-item">
                <div class="img-view">
                  <el-image class="img" :src="teamImg5" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.scientistLi") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistLi_desc1") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistLi_desc2") }}</div>
                  <div class="honor-list">
                    <div class="honor-item">{{ $t("public.scientist_honor1") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLi_honor2") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLi_honor3") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLi_honor4") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLi_honor5") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistLi_honor6") }}</div>
                  </div>
                </div>
              </div>

              <div class="member-item">
                <div class="img-view">
                  <el-image class="img" :src="teamImg6" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.scientistZhou") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistZhou_desc1") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistZhou_desc2") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistZhou_desc3") }}</div>
                  <div class="honor-list">
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhou_honor1") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhou_honor2") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhou_honor3") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhou_honor4") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistZhou_honor5") }}</div>
                  </div>
                </div>
              </div>

              <div class="member-item">
                <div class="img-view">
                  <el-image class="img" :src="teamImg7" fit="cover" lazy />
                </div>
                <div class="user-info">
                  <div class="name">{{ $t("leadershipTeam.scientistChen") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistChen_desc1") }}</div>
                  <div class="desc">{{ $t("leadershipTeam.scientistChen_desc2") }}</div>
                  <div class="honor-list">
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor1") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor2") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor3") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor4") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor5") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor6") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor7") }}</div>
                    <div class="honor-item">{{ $t("leadershipTeam.scientistChen_honor8") }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onBeforeMount, onMounted, onBeforeUnmount, ref, watch } from "vue"
import { useRouter } from "vue-router"

import { i18n } from "@/i18n"

import teamImg1 from "@/assets/leadership-team/team-img1.jpg"
import teamImg2 from "@/assets/leadership-team/team-img2.jpg"
import teamImg3 from "@/assets/leadership-team/team-img3.jpg"
import teamImg4 from "@/assets/leadership-team/team-img4.jpg"
import teamImg5 from "@/assets/leadership-team/team-img5.jpg"
import teamImg6 from "@/assets/leadership-team/team-img6.jpg"
import teamImg7 from "@/assets/leadership-team/team-img7.jpg"

const router = useRouter()

const activeTeamDom = ref("part1")

const lang = ref(i18n.global.locale)

const gotoDomView = (id) => {
  localStorage.setItem("activeTeamDom", id)
  scrollPage(id)
}

const scrollPage = (part) => {
  if (part) {
    activeTeamDom.value = part

    const appScrollbar = document.getElementsByClassName("app-scrollbar")[0]
    appScrollbar.removeEventListener("scroll", () => {})

    const dom = document.getElementById(part)
    dom.scrollIntoView({ behavior: "smooth" })
  }
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

watch(
  () => router.currentRoute.value.query?.part,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      scrollPage(newVal)
    }
  }
)

onBeforeMount(() => {
  const part = localStorage.getItem("activeTeamDom")
  if (part) {
    router.push({ query: { part: part } })
  }
})

onMounted(() => {
  scrollPage(router.currentRoute.value.query?.part)

  localStorage.setItem("activeTeamDom", activeTeamDom.value)
})

onBeforeUnmount(() => {
  const part = localStorage.getItem("activeTeamDom")
  if (part) {
    localStorage.removeItem("activeTeamDom")
  }
})
</script>

<style lang="scss" scoped>
.leadership-team-main {
  .background-img {
    width: 100%;
    height: 490px;
    background: url("@/assets/leadership-team/leadership-team-bg.jpg") no-repeat center center;
    background-size: cover;
    position: relative;
  }

  .bg-desc {
    font-weight: 500;
    font-size: 35px;
    color: #ffffff;
    line-height: 49px;
    letter-spacing: 5px;
    text-align: left;
    font-style: normal;
    max-width: 1320px;
    width: 100%;
    margin: 0 auto;

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);

    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .content {
    background: #f3f3f3;

    .sticky-header {
      background-color: #f3f3f3;
      position: sticky;
      top: 0px;
      z-index: 1000;

      overflow: hidden;

      .card {
        max-width: 1320px;
        width: 100%;
        margin: 0 auto;
        font-size: 18px;
        padding-top: 50px;
        padding-bottom: 15px;
      }

      .title {
        display: flex;
        justify-content: flex-start;
        gap: 75px;

        div {
          position: relative;
          cursor: pointer;
          padding-bottom: 8px;
        }

        .active {
          font-weight: 700;
          border-radius: 2px;

          transition: all 0.3s;

          &::before {
            height: 4px;
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background: #1c2a77;
            border-radius: 2px;
            transition: move 0.3s ease;

            @keyframes move {
              0% {
                transform: translateX(-20px);
                opacity: 0;
              }
              100% {
                transform: translateX(0);
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .cards-view {
      background: #ffffff;
    }

    .cards-main {
      padding: 50px 0;
      max-width: 1320px;
      width: 100%;
      margin: 0 auto;

      background: #ffffff;

      .tip-title {
        h3 {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 600;
          font-size: 36px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 54px;
          letter-spacing: 3px;
          text-align: left;
          font-style: normal;
        }

        h4 {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          font-size: 28px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 42px;
          letter-spacing: 2px;
          text-align: left;
          font-style: normal;
        }
      }

      .cards-part1 {
        .info-main {
          display: flex;
          justify-content: center;
          margin-top: 120px;

          .info-view {
            .img-view {
              width: 250px;
              min-width: 250px;
              height: 375px;

              .img {
                width: 100%;
                height: 100%;
              }
            }

            .user-info {
              display: flex;
              flex-direction: column;
              justify-content: center;
              gap: 5px;
              margin-top: 15px;

              .name {
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 600;
                font-size: 28px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 35px;
                letter-spacing: 2px;
                text-align: center;
                font-style: normal;
              }

              .desc {
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 600;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 35px;
                letter-spacing: 2px;
                text-align: center;
                font-style: normal;
              }
            }
          }
        }
      }

      .cards-part2 {
        margin-top: 130px;

        .member-list {
          margin-top: 120px;
          display: flex;
          flex-direction: column;
          gap: 60px;

          .member-item {
            display: flex;
            align-items: center;
            gap: 92px;

            .img-view {
              width: 250px;
              min-width: 250px;
              height: 375px;

              .img {
                width: 100%;
                height: 100%;
              }
            }

            .user-info {
              .name {
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 600;
                font-size: 28px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 30px;
                letter-spacing: 2px;
                font-style: normal;
                margin-bottom: 10px;
              }

              .desc {
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 600;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 30px;
                letter-spacing: 2px;
                font-style: normal;
              }

              .honor-list {
                margin-top: 35px;

                .honor-item {
                  font-family: SourceHanSansCN, SourceHanSansCN;
                  font-weight: 400;
                  font-size: 18px;
                  color: rgba(0, 0, 0, 0.85);
                  line-height: 28px;
                  letter-spacing: 1px;
                  font-style: normal;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 1320px) {
  .leadership-team-main .content .sticky-header .card,
  .leadership-team-main .content .cards-main,
  .leadership-team-main .bg-desc {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 1200px) {
}

@media (max-width: 1024px) {
}

@media (max-width: 900px) {
  .leadership-team-main .background-img {
    height: 400px;
  }

  .leadership-team-main .bg-desc {
    font-size: 32px;
  }
}

@media (max-width: 820px) {
  .leadership-team-main .content .cards-main .tip-title h3,
  .leadership-team-main .content .cards-main .tip-title h4 {
    text-align: center;
    font-size: 28px;
    line-height: 40px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item {
    flex-direction: column;

    margin-bottom: 60px;
  }

  .leadership-team-main .background-img {
    height: 300px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item {
    gap: 40px;
  }

  .leadership-team-main .content .cards-main .cards-part1 .info-main,
  .leadership-team-main .content .cards-main .cards-part2 .member-list {
    margin-top: 40px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item .user-info {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .leadership-team-main .bg-desc {
    font-size: 30px;
  }
  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item {
    align-items: center;
  }
}

@media (max-width: 576px) {
  .leadership-team-main .content .cards-main .tip-title h3,
  .leadership-team-main .content .cards-main .tip-title h4 {
    text-align: center;
    font-size: 28px;
    line-height: 35px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item .user-info .name {
    font-size: 24px;
  }

  .leadership-team-main .content .cards-main .cards-part1 .info-main .info-view .user-info .name {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .leadership-team-main .bg-desc {
    font-size: 24px;
  }
  .leadership-team-main .content .cards-main .tip-title h3,
  .leadership-team-main .content .cards-main .tip-title h4 {
    text-align: center;
    font-size: 22px;
  }

  .leadership-team-main .content .cards-main .tip-title h4 {
    color: #979797;
  }

  .leadership-team-main .content .cards-main .cards-part1 .info-main {
    margin-top: 26px;
  }

  .leadership-team-main .content .cards-main .cards-part2 {
    margin-top: 100px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item {
    flex-direction: column;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list {
    margin-top: 40px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item {
    gap: 20px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item .user-info .name {
    font-size: 18px;
  }

  .leadership-team-main .content .cards-main .cards-part2 .member-list .member-item .user-info .honor-list .honor-item {
    font-size: 16px;
  }
}

@media (max-width: 430px) {
}

@media (max-width: 390px) {
}
</style>
