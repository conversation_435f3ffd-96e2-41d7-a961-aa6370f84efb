<template>
  <div class="business-segment-main">
    <div class="content">
      <h2 class="segment-title">{{ $t("businessSegment.title") }}</h2>
      <div class="list">
        <el-carousel height="auto" class="business-carousel" motion-blur>
          <el-carousel-item
            :style="`height: ${itemStyleHeight}px`"
            class="carousel-item-style"
            v-for="(item, index) in businessList"
            :key="index"
          >
            <div class="business-carousel-item">
              <div class="carousel-image-view">
                <el-image @load="loadImage" class="carousel-image" :src="item.img" fit="cover" />
              </div>
              <div class="txt-view" v-if="refreshBusiness">
                <div class="title">{{ item[lang].title }}</div>
                <div class="desc" v-html="item[lang].homeDesc"></div>
                <div class="more" @click="gotoBusinessDetails(item.key)">{{ $t("public.more") }}</div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, watch, onMounted, nextTick, onBeforeUpdate } from "vue"
import { i18n } from "@/i18n"
import BusinessSegmentList from "../../business-segment/business-segment-list.js"

import { useRouter } from "vue-router"
const router = useRouter()

const refreshBusiness = ref(true)
const itemStyleHeight = ref(900) // 给个初始值 然后重新计算
const businessList = reactive(BusinessSegmentList)

const lang = ref(i18n.global.locale)

const gotoBusinessDetails = (key) => {
  const pathMap = {
    AcademyOfSciences: "/business-segment/business-details",
    GlobalEnergyValley: "/business-segment/energy-valley-details"
  }
  router.push({ path: pathMap[key] })
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"

    refreshBusiness.value = false

    nextTick(() => {
      refreshBusiness.value = true

      nextTick(() => {
        refreshBusinessList()
      })
    })
  }
)

const refreshBusinessList = () => {
  const businessCarouselItems = document.querySelectorAll(".business-carousel-item")
  let maxHeight = 0
  businessCarouselItems.forEach((item) => {
    maxHeight = Math.max(maxHeight, item.clientHeight)
  })
  itemStyleHeight.value = maxHeight

  loadImgNum.value = 0
}

const loadImgNum = ref(0)
const loadImage = () => {
  loadImgNum.value++
  if (loadImgNum.value === businessList.length) {
    refreshBusinessList()
  }
}

onMounted(() => {
  window.addEventListener("resize", () => {
    refreshBusinessList()
  })
})

onBeforeUpdate(() => {
  window.removeEventListener("resize", () => {})
})
</script>

<style lang="scss" scoped>
.business-segment-main {
  background: #f0f2f5;

  .content {
    width: 1400px;
    max-width: 100%;
    margin: 0 auto;
    padding-top: 120px;

    .segment-title {
      font-size: 50px;
      color: rgba(0, 0, 0, 0.85);
      letter-spacing: 5px;
      text-align: center;
    }

    .list {
      padding-top: 50px;
      padding-bottom: 30px;

      .business-carousel {
        position: relative;

        .carousel-item-style {
        }

        :deep(.el-carousel__arrow--left) {
          left: 20px;
          top: 30%;
        }

        :deep(.el-carousel__arrow--right) {
          right: 20px;
          top: 30%;
        }

        :deep(.el-carousel__container) {
          height: 100%;
        }

        :deep(.el-carousel__arrow) {
          font-size: 70px;
          color: #d2d2d2;
          background: none;
        }

        :deep(.el-carousel__indicators) {
          display: none;
        }

        .business-carousel-item {
          width: 1200px;
          margin: 0 auto;
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .carousel-image {
          display: block;
          width: 100%;
          height: auto;
        }

        .txt-view {
          color: #000000;

          .title {
            font-weight: 600;
            font-size: 42px;
            color: rgba(0, 0, 0, 0.85);
            margin: 40px 0;
          }

          .desc {
            font-size: 18px;
            //letter-spacing: 1px;
            padding-top: 10px;
          }

          .more {
            margin-top: 40px;
            height: 40px;
            width: 130px;
            border-radius: 20px;
            border: 1px solid #1f2a72;
            cursor: pointer;
            font-weight: 600;
            font-size: 18px;
            color: #1f2a72;
            line-height: 40px;
            text-align: center;

            &:hover {
              background-color: #1f2a72;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
@import "./media.scss";
</style>
