<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-19 12:50:29
-->

<template>
  <div class="business home">
    <div class="business-main">
      <div class="content">
        <ThreeCore titleType="threeCore" />
        <IndustrialBase />
        <EnergyValley />
      </div>
    </div>
  </div>
</template>

<script setup>
import ThreeCore from "../home/<USER>/ThreeCore.vue"
import IndustrialBase from "../home/<USER>/IndustrialBase.vue"
import EnergyValley from "../home/<USER>/EnergyValley.vue"

import { i18n } from "@/i18n"
import { nextTick, ref, watch, onMounted } from "vue"

const refresh = ref(true)

// 岁月安好
const langMap = {
  zh: "/resources/video/videoChinese.mp4",
  en: "/resources/video/videoEnglish.mp4",
  zh_hk: "/resources/video/videoChinese.mp4"
}
const VideoChine<PERSON> = ref(langMap[i18n.global.locale])

watch(
  () => i18n.global.locale,
  (newVal) => {
    VideoChinese.value = langMap[newVal]

    refresh.value = false
    nextTick(() => {
      refresh.value = true
    })
  }
)
</script>

<style lang="scss" scoped>
.business {
  .business-main {
    background: #ffffff;

    .content {
      :deep(.business-segment-main .content) {
        padding: 0px;
      }
    }

    .content {
      width: 1400px;
      max-width: 100%;
      margin: 0 auto;

      padding-top: 80px;
      padding-bottom: 200px;

      .segment-title {
        font-size: 50px;
        color: rgba(0, 0, 0, 0.85);
        letter-spacing: 3px;
        text-align: center;
      }
    }
  }
}
@media (max-width: 1320px) {
  .business .business-main .content {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 1024px) {
}

@media (max-width: 820px) {
  .business .business-main .content {
    padding-top: 0;
  }
}

@media (max-width: 768px) {
}

@media (max-width: 576px) {
}

@media (max-width: 480px) {
}

@media (max-width: 380px) {
}
</style>
