<template>
  <div class="tangka-list">
    <div class="tangka-list-main">
      <div class="title">{{ $t("tangka.famousWorks") }}</div>
      <div class="content">
        <div class="item" v-for="item in worksList" :key="item.id" @click="gotoDetails(item.id)">
          <div class="img-view">
            <el-image :src="item.zipImg" lazy />
          </div>
          <div class="name">{{ item[lang].title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue"
import worksList from "./works-list"
import { i18n } from "@/i18n"
import { watch } from "vue"
import { useRouter } from "vue-router"

const router = useRouter()
const lang = ref(i18n.global.locale)

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const gotoDetails = (id) => {
  router.push({ name: "TangkaDetails", query: { id } })
}
</script>
<style lang="scss" scoped>
.tangka-list {
  // background: #ffffff;
  background: f2f3f5;

  .tangka-list-main {
    max-width: 1320px;
    width: 100%;
    margin: 0 auto;
    padding: 80px 0;

    .title {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 600;
      font-size: 36px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 54px;
      letter-spacing: 3px;
      text-align: center;
      font-style: normal;
      margin-bottom: 100px;
    }

    .content {
      column-count: 4;
      column-gap: 40px;

      .item {
        break-inside: avoid;
        margin-bottom: 40px;
        cursor: pointer;

        .img-view {
          width: 100%;
          el-image {
            width: 100%;
            height: 100%;
          }
        }

        .name {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #000000;
          line-height: 23px;
          text-align: center;
          margin-top: 16px;
          display: -webkit-box;
          overflow: hidden;
          -webkit-box-orient: vertical;
          box-sizing: border-box;
          -webkit-line-clamp: 1;
        }
      }
    }
  }
}

@media (max-width: 1320px) {
  .tangka-list .tangka-list-main {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 1200px) {
}

@media (max-width: 1024px) {
}

@media (max-width: 820px) {
  .tangka-list .tangka-list-main .content {
    column-count: 3;
  }
}

@media (max-width: 768px) {
  .tangka-list .tangka-list-main {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .tangka-list .tangka-list-main .content {
    column-count: 2;
  }
}

@media (max-width: 576px) {
}
@media (max-width: 480px) {
  .tangka-list .tangka-list-main .content {
    column-count: 1;
  }
}
@media (max-width: 390px) {
}
@media (max-width: 380px) {
}
</style>
