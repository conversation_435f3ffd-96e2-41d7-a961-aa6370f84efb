<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-04 17:50:36
-->

<template>
  <footer class="layout-footer">
    <div class="footer-main">
      <div class="footer-view">
        <div class="item-box">
          <div class="title">{{ $t("menus.aboutUs") }}</div>
          <div class="content link">
            <div class="item" @click="gotoAbout('part1')">{{ $t("aboutUs.companyProfile") }}</div>

            <!-- 2025-6-1 add by zhw -->
             <div class="item" @click="gotoAbout('part1_1')">{{ $t("aboutUs.threeAreasTitle") }}</div>
             <div class="item" @click="gotoAbout('part1_2')">{{ $t("aboutUs.scienceTitle") }}</div>

            <div class="item" @click="gotoAbout('part2')">{{ $t("aboutUs.visionAndCulture") }}</div>
            <div class="item" @click="gotoAbout('part3')">{{ $t("aboutUs.companyCulture") }}</div>
            <!-- 2025-6-1 by zhw -->
            <!-- <div class="item" @click="gotoAbout('part4')">{{ $t("aboutUs.chairmanMessage") }}</div> -->
            <div class="item" @click="gotoAbout('part7')">{{ $t("aboutUs.organizationalStructure") }}</div>
            <div class="item" @click="gotoAbout('part7_1')">{{ $t("aboutUs.officeEnvTitle") }}</div>
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box">
          <div class="title">{{ $t("menus.businessSegment") }}</div>
          <div class="content link">
            <!-- 2025-6-1 by zhw -->
            <!-- <div class="item title-item" @click="gotoBusinessDetails()">
              {{ $t("home.scientificResearchSection") }}
            </div> -->
            <div class="item title-item" @click="gotoBusinessDetails()">
              {{ $t("home.scienceTitle") }}
            </div>
            <div class="item title-item" @click="gotoIndustrialBaseDetails()">
              {{ $t("home.industrialBase") }}
            </div>
            <div class="item title-item" @click="gotoEnergyValleyDetails()">
              {{ $t("home.energyValley") }}
            </div>
            <!-- 2025-6-1 by zhw -->
            <div class="item" @click="gotoBusinessDetails()">生态与可持续发展</div>
            <div class="item" @click="gotoBusinessDetails()">生物医疗</div>
            <div class="item" @click="gotoBusinessDetails()">能源与安全</div>
            <div class="item title-item" @click="gotoNews()">
              {{ $t("menus.news") }}
            </div>
            <div class="item title-item" @click="gotoTangka()">
              {{ $t("menus.tangKa") }}
            </div>
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box">
          <div class="title">{{ $t("contactUs.title") }}</div>
          <div class="content">
            <div class="item">
              <el-icon><MapLocation /></el-icon><span class="address">{{ $t("contactUs.address1") }}</span>
            </div>
            <div class="item">
              <el-icon><Phone /></el-icon> <span>+86 0755-27211126</span>
            </div>
            <div class="item">
              <el-icon><Message /></el-icon> <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box">
          <div class="logo">
            <el-image :src="logoImg" fit="cover" lazy />
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box-qr">
          <div class="QR-code">
            <el-image class="img" :src="wechatQRcodeImg" fit="cover" lazy />
          </div>
          <p class="desc">{{ $t("footer.groupName") }}</p>
          <p class="desc">{{ $t("footer.wechatQRcode") }}</p>
        </div>
      </div>
    </div>
    <div class="footer-tip">© 2024 by sinofortune.co. All rights reserved!</div>
  </footer>
</template>

<script setup>
import { useRouter } from "vue-router"
import logoImg from "@/assets/images/logo.png"
import wechatQRcodeImg from "@/assets/images/wechat-QRcode-img.jpg"
import BusinessSegmentList from "@/views/business-segment/business-segment-list.js"

import { reactive, watch, ref } from "vue"
import { i18n } from "@/i18n"

const businessText = reactive(BusinessSegmentList)
const router = useRouter()

const lang = ref(i18n.global.locale)
watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const gotoAbout = (part) => {
  router.push({ name: "About", query: { part } })
}

const gotoLeadershipTeam = () => {
  router.push({ path: "/leadership-team", query: { part: "part1" } })
}

const gotoBusinessDetails = () => {
  router.push({ path: "/business-segment/business-details" })
}

const gotoIndustrialBaseDetails = () => {
  router.push({ path: "/business-segment/industrial-base-details" })
}

const gotoEnergyValleyDetails = () => {
  router.push({ path: "/business-segment/energy-valley-details" })
}
const gotoNews = () => {
  router.push({ path: "/news" })
}
const gotoTangka = () => {
  router.push({ path: "/tangka" })
}
</script>

<style lang="scss" scoped>
.layout-footer {
  background: #1c2a77;

  .footer-main {
    min-height: 300px;
    max-width: 1320px;
    width: 100%;
    margin: 0 auto;

    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 50px;

    padding: 70px 0;

    .footer-view {
      display: flex;
      justify-content: space-between;
    }

    .item-box {
      display: flex;
      flex-direction: column;
      //align-items: center;
      gap: 20px;

      .title {
        font-size: 18px;
        color: #ffffff;
        font-weight: 600;
        letter-spacing: 1px;
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .item {
          display: flex;
          align-items: center;
          max-width: 360px;
          font-size: 16px;
          color: #f0f0f0;
          width: fit-content;

          span {
            margin-left: 10px;
          }
        }
      }

      .link {
        .item {
          cursor: pointer;

          &:hover:not(.title-item) {
            text-decoration: underline;
          }
        }

        .title-item {
          font-weight: 600;
          font-size: 18px;
        }
      }
    }

    .item-box-qr {
      display: flex;
      flex-direction: column;
      width: 190px;

      .desc {
        font-size: 16px;
        color: #f0f0f0;
        text-align: center;
        font-weight: 600;
        letter-spacing: 1px;
      }
    }
  }

  .footer-tip {
    color: #ffffff;
    text-align: center;
    padding: 10px 0;
  }
}

@media (max-width: 1320px) {
  .layout-footer .footer-main {
    padding: 70px 35px;
    gap: 50px;
    flex-wrap: wrap;
  }
  .layout-footer .footer-main .footer-view {
    flex: 0 0 44%;

    display: block;
  }
}
@media (max-width: 1024px) {
}
@media (max-width: 820px) {
}
@media (max-width: 768px) {
  .layout-footer .footer-main {
    flex-direction: column;
    align-items: center;
  }
  .layout-footer .footer-main .item-box .content .item {
    justify-content: center;
  }
  .layout-footer .footer-main .item-box .title {
    text-align: center;
  }

  .layout-footer .footer-main .item-box .content {
    align-items: center;
  }
}
@media (max-width: 576px) {
}
@media (max-width: 480px) {
  .layout-footer .footer-main {
    padding: 70px 10px;
  }
}
@media (max-width: 390px) {
  .layout-footer .footer-main .item-box .content .item {
    font-size: 14px;
  }
}
</style>
