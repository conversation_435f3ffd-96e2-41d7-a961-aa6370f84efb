/**
 * 响应式布局组合式函数
 * 提供常用的响应式布局功能
 */

import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { useBreakpoints, useResponsiveValue } from '@/hooks/useResponsive'
import { responsive } from '@/utils/responsive'

/**
 * 响应式网格布局
 * @param {Object} options - 配置选项
 * @returns {Object} 网格布局相关的响应式状态和方法
 */
export function useResponsiveGrid(options = {}) {
  const {
    columns = { xs: 1, sm: 2, md: 3, lg: 4 },
    gap = { xs: '1rem', sm: '1.5rem', md: '2rem' },
    minItemWidth = '250px',
    autoFit = true
  } = options

  const { currentBreakpoint } = useBreakpoints()

  // 响应式列数
  const responsiveColumns = useResponsiveValue(columns)
  
  // 响应式间距
  const responsiveGap = useResponsiveValue(gap)

  // 网格样式
  const gridStyles = computed(() => {
    const styles = {
      display: 'grid',
      gap: responsiveGap.value
    }

    if (autoFit && minItemWidth) {
      styles.gridTemplateColumns = `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`
    } else {
      styles.gridTemplateColumns = `repeat(${responsiveColumns.value}, 1fr)`
    }

    return styles
  })

  // 网格类名
  const gridClasses = computed(() => [
    'responsive-grid',
    `grid-cols-${responsiveColumns.value}`,
    `grid-${currentBreakpoint.value}`
  ])

  return {
    gridStyles,
    gridClasses,
    columns: responsiveColumns,
    gap: responsiveGap,
    currentBreakpoint
  }
}

/**
 * 响应式 Flexbox 布局
 * @param {Object} options - 配置选项
 * @returns {Object} Flex 布局相关的响应式状态和方法
 */
export function useResponsiveFlex(options = {}) {
  const {
    direction = { xs: 'column', md: 'row' },
    wrap = 'wrap',
    justify = 'flex-start',
    align = 'stretch',
    gap = { xs: '1rem', md: '2rem' }
  } = options

  const { currentBreakpoint } = useBreakpoints()

  // 响应式方向
  const responsiveDirection = useResponsiveValue(direction)
  
  // 响应式间距
  const responsiveGap = useResponsiveValue(gap)

  // Flex 样式
  const flexStyles = computed(() => ({
    display: 'flex',
    flexDirection: responsiveDirection.value,
    flexWrap: wrap,
    justifyContent: justify,
    alignItems: align,
    gap: responsiveGap.value
  }))

  // Flex 类名
  const flexClasses = computed(() => [
    'responsive-flex',
    `flex-${responsiveDirection.value}`,
    `flex-${currentBreakpoint.value}`
  ])

  return {
    flexStyles,
    flexClasses,
    direction: responsiveDirection,
    gap: responsiveGap,
    currentBreakpoint
  }
}

/**
 * 响应式容器
 * @param {Object} options - 配置选项
 * @returns {Object} 容器相关的响应式状态和方法
 */
export function useResponsiveContainer(options = {}) {
  const {
    maxWidth = { sm: '640px', md: '768px', lg: '992px', xl: '1200px', xxl: '1320px' },
    padding = { xs: '1rem', md: '2rem', xl: '4rem' },
    center = true,
    fluid = false
  } = options

  const { currentBreakpoint } = useBreakpoints()

  // 响应式最大宽度
  const responsiveMaxWidth = useResponsiveValue(maxWidth)
  
  // 响应式内边距
  const responsivePadding = useResponsiveValue(padding)

  // 容器样式
  const containerStyles = computed(() => {
    const styles = {
      width: '100%',
      paddingLeft: responsivePadding.value,
      paddingRight: responsivePadding.value
    }

    if (!fluid) {
      styles.maxWidth = responsiveMaxWidth.value
    }

    if (center) {
      styles.marginLeft = 'auto'
      styles.marginRight = 'auto'
    }

    return styles
  })

  // 容器类名
  const containerClasses = computed(() => {
    const classes = ['responsive-container']
    
    if (fluid) {
      classes.push('container-fluid')
    }
    
    if (center) {
      classes.push('container-center')
    }
    
    classes.push(`container-${currentBreakpoint.value}`)
    
    return classes
  })

  return {
    containerStyles,
    containerClasses,
    maxWidth: responsiveMaxWidth,
    padding: responsivePadding,
    currentBreakpoint
  }
}

/**
 * 响应式字体
 * @param {Object} options - 配置选项
 * @returns {Object} 字体相关的响应式状态和方法
 */
export function useResponsiveFont(options = {}) {
  const {
    size = { xs: '14px', sm: '16px', md: '18px', lg: '20px' },
    lineHeight = 1.5,
    weight = 'normal',
    useClamp = true
  } = options

  // 响应式字体大小
  const responsiveSize = useResponsiveValue(size)

  // 字体样式
  const fontStyles = computed(() => {
    const styles = {
      fontWeight: weight
    }

    if (useClamp && typeof window !== 'undefined') {
      const minSize = size.xs || '14px'
      const maxSize = size.xl || size.lg || '20px'
      const preferredSize = responsiveSize.value
      styles.fontSize = `clamp(${minSize}, ${preferredSize}, ${maxSize})`
    } else {
      styles.fontSize = responsiveSize.value
    }

    if (typeof lineHeight === 'number') {
      styles.lineHeight = lineHeight
    } else {
      styles.lineHeight = lineHeight
    }

    return styles
  })

  return {
    fontStyles,
    fontSize: responsiveSize
  }
}

/**
 * 响应式间距
 * @param {Object} options - 配置选项
 * @returns {Object} 间距相关的响应式状态和方法
 */
export function useResponsiveSpacing(options = {}) {
  const {
    margin = { xs: '1rem', md: '2rem' },
    padding = { xs: '1rem', md: '2rem' },
    gap = { xs: '0.5rem', md: '1rem' }
  } = options

  // 响应式间距值
  const responsiveMargin = useResponsiveValue(margin)
  const responsivePadding = useResponsiveValue(padding)
  const responsiveGap = useResponsiveValue(gap)

  // 间距样式
  const spacingStyles = computed(() => ({
    margin: responsiveMargin.value,
    padding: responsivePadding.value,
    gap: responsiveGap.value
  }))

  return {
    spacingStyles,
    margin: responsiveMargin,
    padding: responsivePadding,
    gap: responsiveGap
  }
}

/**
 * 响应式可见性
 * @param {Object} options - 配置选项
 * @returns {Object} 可见性相关的响应式状态和方法
 */
export function useResponsiveVisibility(options = {}) {
  const {
    show = { xs: true, sm: true, md: true, lg: true },
    hideOn = [],
    showOn = []
  } = options

  const { currentBreakpoint, deviceType } = useBreakpoints()

  // 是否可见
  const isVisible = computed(() => {
    // 检查 hideOn 配置
    if (hideOn.includes(currentBreakpoint.value) || hideOn.includes(deviceType.value)) {
      return false
    }

    // 检查 showOn 配置
    if (showOn.length > 0) {
      return showOn.includes(currentBreakpoint.value) || showOn.includes(deviceType.value)
    }

    // 检查 show 配置
    const shouldShow = useResponsiveValue(show)
    return shouldShow.value
  })

  // 可见性样式
  const visibilityStyles = computed(() => ({
    display: isVisible.value ? '' : 'none'
  }))

  // 可见性类名
  const visibilityClasses = computed(() => {
    const classes = []
    
    if (!isVisible.value) {
      classes.push('hidden')
    }
    
    hideOn.forEach(breakpoint => {
      classes.push(`hide-${breakpoint}`)
    })
    
    showOn.forEach(breakpoint => {
      classes.push(`show-${breakpoint}`)
    })
    
    return classes
  })

  return {
    isVisible,
    visibilityStyles,
    visibilityClasses
  }
}

/**
 * 响应式图片
 * @param {Object} options - 配置选项
 * @returns {Object} 图片相关的响应式状态和方法
 */
export function useResponsiveImage(options = {}) {
  const {
    src,
    sizes = { xs: '100vw', sm: '50vw', md: '33vw', lg: '25vw' },
    lazy = true,
    placeholder = ''
  } = options

  const { currentBreakpoint } = useBreakpoints()

  // 响应式尺寸
  const responsiveSizes = useResponsiveValue(sizes)

  // 图片属性
  const imageAttrs = computed(() => {
    const attrs = {
      src: src,
      loading: lazy ? 'lazy' : 'eager'
    }

    if (placeholder) {
      attrs['data-placeholder'] = placeholder
    }

    return attrs
  })

  // 图片样式
  const imageStyles = computed(() => ({
    width: responsiveSizes.value,
    height: 'auto',
    display: 'block'
  }))

  return {
    imageAttrs,
    imageStyles,
    sizes: responsiveSizes,
    currentBreakpoint
  }
}

export default {
  useResponsiveGrid,
  useResponsiveFlex,
  useResponsiveContainer,
  useResponsiveFont,
  useResponsiveSpacing,
  useResponsiveVisibility,
  useResponsiveImage
}
