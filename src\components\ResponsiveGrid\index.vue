<!--
 * 响应式网格组件
 * 提供自适应的网格布局，支持不同设备的列数配置
 -->
<template>
  <div 
    :class="gridClasses"
    :style="gridStyles"
    class="responsive-grid"
  >
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useResponsiveValue, useResponsiveClass } from '@/hooks/useResponsive'

const props = defineProps({
  // 各断点的列数配置
  cols: {
    type: [Number, Object],
    default: () => ({
      xs: 1,
      sm: 2,
      md: 2,
      lg: 3,
      xl: 4,
      xxl: 4
    })
  },
  // 网格间距
  gap: {
    type: [String, Object],
    default: () => ({
      xs: '1rem',
      sm: '1rem',
      md: '1.5rem',
      lg: '1.5rem',
      xl: '2rem',
      xxl: '2rem'
    })
  },
  // 网格类型
  type: {
    type: String,
    default: 'grid',
    validator: (value) => ['grid', 'flex', 'masonry'].includes(value)
  },
  // 对齐方式
  align: {
    type: String,
    default: 'stretch',
    validator: (value) => ['start', 'center', 'end', 'stretch'].includes(value)
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  }
})

// 响应式列数
const responsiveCols = useResponsiveValue(
  typeof props.cols === 'number' 
    ? { default: props.cols }
    : props.cols
)

// 响应式间距
const responsiveGap = useResponsiveValue(
  typeof props.gap === 'string' 
    ? { default: props.gap }
    : props.gap
)

// 响应式类名
const responsiveClasses = useResponsiveClass({
  mobile: 'grid-mobile',
  tablet: 'grid-tablet',
  desktop: 'grid-desktop'
})

// 计算网格类名
const gridClasses = computed(() => {
  const classes = [
    responsiveClasses.value,
    `grid-${props.type}`,
    `grid-align-${props.align}`
  ]
  
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  return classes
})

// 计算网格样式
const gridStyles = computed(() => {
  const styles = {
    gap: responsiveGap.value
  }
  
  if (props.type === 'grid') {
    styles.display = 'grid'
    styles.gridTemplateColumns = `repeat(${responsiveCols.value}, 1fr)`
  } else if (props.type === 'flex') {
    styles.display = 'flex'
    styles.flexWrap = 'wrap'
    styles.margin = `calc(-${responsiveGap.value} / 2)`
  }
  
  return styles
})
</script>

<style lang="scss" scoped>
.responsive-grid {
  width: 100%;
  
  // 网格类型样式
  &.grid-grid {
    display: grid;
    
    &.grid-align-start {
      align-items: start;
    }
    
    &.grid-align-center {
      align-items: center;
    }
    
    &.grid-align-end {
      align-items: end;
    }
    
    &.grid-align-stretch {
      align-items: stretch;
    }
  }
  
  &.grid-flex {
    display: flex;
    flex-wrap: wrap;
    
    :deep(> *) {
      flex: 1;
      min-width: 0;
    }
    
    &.grid-align-start {
      align-items: flex-start;
    }
    
    &.grid-align-center {
      align-items: center;
    }
    
    &.grid-align-end {
      align-items: flex-end;
    }
    
    &.grid-align-stretch {
      align-items: stretch;
    }
  }
  
  &.grid-masonry {
    column-count: var(--grid-cols, 3);
    column-gap: var(--grid-gap, 1.5rem);
    
    :deep(> *) {
      break-inside: avoid;
      margin-bottom: var(--grid-gap, 1.5rem);
    }
    
    @include mobile-only {
      column-count: 1;
    }
    
    @include tablet-only {
      column-count: 2;
    }
  }
  
  // 设备特定样式
  &.grid-mobile {
    @include mobile-only {
      gap: 1rem;
    }
  }
  
  &.grid-tablet {
    @include tablet-only {
      gap: 1.5rem;
    }
  }
  
  &.grid-desktop {
    @include desktop-only {
      gap: 2rem;
    }
  }
}

// 网格项样式
.grid-item {
  width: 100%;
  
  // Flex 布局下的网格项
  .grid-flex & {
    padding: calc(var(--grid-gap, 1.5rem) / 2);
  }
}
</style>
