<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-18 17:59:35
-->

<template>
  <div class="industrial-base-details-main">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("home.industrialBase") }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="content">
      <div class="info">
        <div class="title">{{ $t("home.industrialBase") }}</div>
        <div class="desc">{{ $t("home.base_desc1") }}</div>
        <div class="desc" v-html="$t('home.base_desc2')"></div>
      </div>

      <div class="img-view">
        <el-image class="img" :src="industrialBaseDetails" fit="cover" lazy />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowRight } from "@element-plus/icons-vue"
import industrialBaseDetails from "@/assets/business-segment/industrial-base-details-bg.jpg"
</script>

<style lang="scss" scoped>
.industrial-base-details-main {
  .sub-title {
    height: 100px;
    background: #f3f3f3;

    .top-breadcrumb {
      width: 1320px;
      max-width: 100%;
      margin: 0 auto;
      padding-top: 50px;

      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        font-size: 18px;

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content {
    position: relative;
    display: flex;
    justify-content: center;

    .info {
      position: absolute;
      z-index: 200;
      top: 105px;

      width: 1200px;
      max-width: 100%;
      margin: 0 auto;
      color: #ffffff;

      .title {
        min-width: 400px;
        font-weight: 600;
        font-size: 35px;
        line-height: 49px;
        letter-spacing: 1px;
        margin-bottom: 30px;
      }

      .desc {
        font-size: 18px;
        line-height: 28px;
      }
    }

    .img-view {
      width: 100%;

      .img {
        display: block;
        width: 100%;
        height: auto;
      }
    }
  }
}

@media (max-width: 1320px) {
  .industrial-base-details-main .sub-title .top-breadcrumb {
    padding-left: 20px;
    padding-right: 20px;
  }

  .industrial-base-details-main .content .img-view {
    width: 100%;
    height: auto;
  }

  .industrial-base-details-main .content .info {
    padding: 0 20px;
  }
}

@media (max-width: 1024px) {
  .industrial-base-details-main .content .info .title {
    font-size: 28px;
    line-height: 38px;
    min-width: 300px;
  }
  .industrial-base-details-main .content .info .desc {
    font-size: 18px;
  }

  .industrial-base-details-main .content .info {
    top: 85px;
  }
}

@media (max-width: 820px) {
  .industrial-base-details-main .content .info {
    flex-direction: column;
    gap: 30px;
  }

  .industrial-base-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
  }

  .industrial-base-details-main .content .img-view {
    margin-top: 20px;
  }

  .industrial-base-details-main .content .info {
    top: 55px;
  }
  .industrial-base-details-main .content .info .title {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .industrial-base-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
    line-height: 14px;
  }
}

@media (max-width: 576px) {
  .industrial-base-details-main .content .info .title {
    font-size: 24px;
    line-height: 34px;
  }

  .industrial-base-details-main .content .info .desc {
    font-size: 16px;
  }

  .industrial-base-details-main .content .info .title {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  :deep(.el-carousel__arrow--right) {
    top: 13%;
  }

  .industrial-base-details-main .content .img-view {
    width: 100%;
    height: 300px;
  }

  .industrial-base-details-main .content .img-view .img {
    width: 100%;
    height: 100%;
  }
}

@media (max-width: 390px) {
  .industrial-base-details-main .content .info .title {
    font-size: 22px;
  }

  :deep(.el-carousel__indicators--horizontal) {
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
