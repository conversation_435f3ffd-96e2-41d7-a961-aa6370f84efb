<template>
  <div class="home-carousel-view">
    <el-carousel class="home-carousel" motion-blur :autoplay="false">
      <el-carousel-item v-for="(item, index) in carouselList" :key="index" @click="gotoGreatHealth(item)">
        <div class="home-greatHealth-view" v-if="item?.type == 'greatHealth'">
          <div class="txt-s">{{ item[lang].desc1 }}</div>
          <div class="txt-s">{{ item[lang].desc2 }}</div>
          <div class="txt-tip">{{ item[lang].desc3 }}</div>

          <div class="view-tips">
            <div class="tips-i">
              <span>{{ item[lang].tipTxt }}</span
              ><el-icon><Right /></el-icon>
            </div>
          </div>
        </div>
        <div v-else class="txt">{{ item[lang] }}</div>
        <el-image class="carousel-image" :src="item.img" fit="cover" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script setup>
import { ref, watch } from "vue"
import homeBg from "@/assets/home/<USER>"
import homeBg2 from "@/assets/home/<USER>"
import homeImg1 from "@/assets/home/<USER>"
import goodLifeService from "@/assets/home/<USER>"

import { i18n } from "@/i18n"
import { useRouter } from "vue-router"

const router = useRouter()

const lang = ref(i18n.global.locale)

const carouselList = ref([
  {
    img: goodLifeService,
    zh: "美好生活服务商",
    en: "Good Life Service Provider",
    zh_hk: "美好生活服務商",
  },
    {
    img: homeImg1,
    zh: {
      desc1: "中兆国际集团",
      desc2: "大健康板块隆重上市",
      desc3: "各界大咖明星齐祝福、共庆辉煌时刻",
      tipTxt: "前往观看视频"
    },
    en: {
      desc1: "Zhongzhao International Group",
      desc2: "The Comprehensive Health Segment is Officially Launched",
      desc3: "Wishes from Celebrities to Mark this Moment",
      tipTxt: "Watch the video"
    },
    zh_hk: {
      desc1: "中兆國際集團",
      desc2: "大健康板塊隆重上市",
      desc3: "各界大咖明星齊祝福、共慶輝煌時刻",
      tipTxt: "前往觀看視頻"
    },
    type: "greatHealth"
  },
  {
    img: homeBg,
    zh: "安全 健康 可持续发展",
    en: "Safe healthy and sustainable development",
    zh_hk: "安全 健康 可持續發展"
  },
  {
    img: homeBg2,
    zh: "成为全人类安全 健康和环境的坚定支持者",
    en: "To be a strong supporter of safety, health and the environment for all",
    zh_hk: "成為全人類安全 健康和環境的堅定支持者"
  }
])

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const gotoGreatHealth = (item) => {
  if (item?.type == "greatHealth") {
    router.push({ path: "/great-health" })
  }
}
</script>
<style lang="scss" scoped>
.home-carousel-view {
  .home-carousel {
    @include responsive-spacing(height, 400px, 500px, 680px);
    position: relative;
    background: #000000;

    :deep(.el-carousel__container) {
      height: 100%;
    }

    :deep(.el-carousel__arrow) {
      top: 50%;
      @include responsive-font-size(24px, 32px, 40px);
      color: #d2d2d2;
      background: none;

      // 移动端隐藏箭头
      @include mobile-only {
        display: none;
      }
    }

    :deep(.el-carousel__indicators) {
      .el-carousel__button {
        height: 6px;
        border-radius: 5px;

        @include mobile-only {
          width: 20px;
          height: 4px;
        }
      }
    }

    :deep(.el-carousel__arrow--right) {
      @include responsive-spacing(right, 5%, 8%, 10%);
    }

    :deep(.el-carousel__arrow--left) {
      @include responsive-spacing(left, 5%, 8%, 10%);
    }

    .home-greatHealth-view,
    .txt {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      @include responsive-font-size(24px, 32px, 40px);
      color: #ffffff;
      letter-spacing: 3px;
      z-index: 1;
      font-weight: 600;
      text-shadow: 1px 1px 10px rgba(#ffffff, 0.1);
      text-align: center;

      // 响应式宽度
      @include mobile-only {
        width: 90%;
        padding: 0 20px;
        letter-spacing: 1px;
      }

      @include tablet-only {
        width: 80%;
        padding: 0 40px;
        letter-spacing: 2px;
      }

      @include desktop-only {
        width: 1200px;
      }
    }

    .home-greatHealth-view {
      cursor: pointer;

      .txt-tip {
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 500;
        font-size: 28px;
        color: #90d6ff;
        line-height: 50px;
        letter-spacing: 2px;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
      }

      .view-tips {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .tips-i {
          display: flex;
          align-items: center;
          gap: 5px;
          margin-top: 50px;
          cursor: pointer;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          letter-spacing: 1px;
          text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .el-icon {
          font-size: 30px;
        }
      }
    }

    .txt {
      width: 100%;
      text-align: center;
    }

    .carousel-image {
      display: block;
      width: 100%;
      height: 100%;
    }

    video {
      display: block;
      width: 100%;
      height: 100%;

      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      object-fit: unset;

      z-index: 50;
    }
  }
}
@import "./media.scss";
</style>
