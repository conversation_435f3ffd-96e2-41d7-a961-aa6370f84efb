<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-25 11:50:14
-->

<template>
  <div class="science-academy-details-main">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ researchInstituteInfo[lang].title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <div class="content-main">
      <div class="content">
        <div class="info">
          <div class="title">{{ researchInstituteInfo[la<PERSON>].title }}</div>
          <div class="desc" v-html="researchInstituteInfo[lang].homeDesc"></div>
        </div>

        <div class="child-content">
          <div class="tabs-list" v-if="researchInstituteInfo.productList?.length > 1">
            <div class="tabs-content">
              <div
                class="item"
                :class="{ 'item-active': index == activeIndex }"
                v-for="(item, index) in researchInstituteInfo.productList"
                :key="index"
                @click="activeIndex = index"
              >
                {{ item.title[lang] }}
              </div>
            </div>
          </div>
          <div class="item-list">
            <template v-for="(item, index) in researchInstituteInfo.productList" :key="index">
              <div class="item-content" v-if="activeIndex == index">
                <div
                  class="item"
                  :class="{ 'item-single': item.products.length == 1 }"
                  v-for="(_item, _index) in item.products"
                  :key="_index"
                >
                  <div class="item-img">
                    <el-image class="img" :src="_item.img" fit="cover" lazy />
                  </div>
                  <div class="item-title">{{ _item[lang].title }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import { ArrowRight } from "@element-plus/icons-vue"
import { onBeforeMount } from "vue"

import academySciencesList from "../academy-sciences-list"

import { useRouter } from "vue-router"

import { i18n } from "@/i18n"
import { watch } from "vue"

const router = useRouter()

const lang = ref(i18n.global.locale)

const activeIndex = ref(0)

let researchInstituteInfo = ref({})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

watch(
  () => router.currentRoute.value.query.key,
  (newVal) => {
    updateBusinessSegmentInfo(newVal)
  }
)

const updateBusinessSegmentInfo = (key) => {
  const foundSegment = academySciencesList.find((item) => item.key === key)
  if (foundSegment) {
    researchInstituteInfo.value = foundSegment
  }
}

onBeforeMount(() => {
  const key = router.currentRoute.value.query.key
  updateBusinessSegmentInfo(key)
})
</script>

<style lang="scss" scoped>
.science-academy-details-main {
  .sub-title {
    height: 100px;
    background: #f3f3f3;

    .top-breadcrumb {
      width: 1320px;
      max-width: 100%;
      margin: 0 auto;
      padding-top: 50px;

      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        font-size: 18px;

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #ffffff;
  }
 
  .content {
    width: 1200px;
    max-width: 100%;
    margin: 0 auto;
    padding: 150px 0;

    .info {
      display: flex;
      align-items: flex-start;
      gap: 50px;

      .title {
        min-width: 400px;
        font-weight: 600;
        font-size: 35px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 1px;
      }

      .desc {
        font-size: 18px;
        color: #000000;
        line-height: 28px;
        //letter-spacing: 1px;
      }
    }

    .child-content {
      padding: 100px 0;

      .tabs-list {
        margin-bottom: 135px;

        .tabs-content {
          display: flex;
          gap: 56px;

          .item {
            height: 90px;
            padding: 0 30px;
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 400;
            font-size: 24px;
            color: #000000;
            line-height: 90px;
            text-align: center;
            font-style: normal;
            cursor: pointer;

            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .item-active {
            background: #078cec;
            color: #ffffff;
          }
        }
      }

      .item-list {
        .item-content {
          display: flex;
          flex-wrap: wrap;
          row-gap: 60px;
          column-gap: 120px;

          .item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;

            padding: 0 50px;
            position: relative;

            .item-img {
              width: 400px;
              height: auto;
              position: relative;
              z-index: 10;

              .img {
                width: 100%;
                height: 100%;
              }
            }

            .item-title {
              position: relative;
              z-index: 10;
              max-width: 400px;
              font-family: SourceHanSansCN, SourceHanSansCN;
              font-weight: 400;
              font-size: 28px;
              color: #000000;
              line-height: 42px;
              text-align: center;
              font-style: normal;
              padding-bottom: 20px;
            }

            &::before {
              content: "";
              position: absolute;
              bottom: 0;
              width: 100%;
              height: 300px;
              background: #f3f3f3;
              z-index: 5;
            }
          }

          .item-single {
            margin: 0 auto;
          }
        }
      }
    }
  }
}

@media (max-width: 1320px) {
  .science-academy-details-main .sub-title .top-breadcrumb,
  .science-academy-details-main .content {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 1024px) {
  .science-academy-details-main .content .info .title {
    font-size: 28px;
    line-height: 38px;
    min-width: 300px;
  }
  .science-academy-details-main .content .info .desc {
    font-size: 18px;
  }
}

@media (max-width: 820px) {
  .science-academy-details-main .content .info {
    flex-direction: column;
    gap: 30px;
  }

  .science-academy-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
  }

  .science-academy-details-main .content {
    padding-top: 80px;
    padding-bottom: 0px;
  }

  .science-academy-details-main .content .child-content .item-list .item-content .item .item-title {
    font-size: 24px;
  }

  .science-academy-details-main .content .child-content .tabs-list .tabs-content {
    flex-direction: column;
    gap: 20px;
  }

  .science-academy-details-main .content .child-content .tabs-list .tabs-content .item {
    height: 60px;
    line-height: 60px;
  }
}

@media (max-width: 768px) {
  .science-academy-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
    line-height: 14px;
  }
}

@media (max-width: 576px) {
  .science-academy-details-main .content .info .title {
    font-size: 24px;
    line-height: 34px;
  }

  .science-academy-details-main .content .info .desc {
    font-size: 16px;
  }

  .science-academy-details-main .content .child-content .item-list .item-content .item .item-img {
    width: 100%;
    height: auto;
  }

  .science-academy-details-main .content .child-content .item-list .item-content .item .item-title {
    font-size: 20px;
  }

  .science-academy-details-main .content .child-content .tabs-list .tabs-content .item {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  :deep(.el-carousel__arrow--right) {
    top: 13%;
  }
}

@media (max-width: 390px) {
  .science-academy-details-main .content .info .title {
    font-size: 22px;
  }

  :deep(.el-carousel__indicators--horizontal) {
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
