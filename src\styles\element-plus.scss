/** 自定义 Element Plus 样式 */

// 卡片
.el-card {
  background-color: var(--el-bg-color);
}

// 分页 - 使用统一的响应式断点
.el-pagination {
  // 移动端隐藏部分分页元素
  @include mobile-only {
    .el-pagination__total,
    .el-pagination__sizes,
    .el-pagination__jump,
    .btn-prev,
    .btn-next {
      display: none !important;
    }

    // 移动端分页样式优化
    .el-pagination__pager {
      .number {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
      }
    }
  }

  // 平板端适配
  @include tablet-only {
    .el-pagination__total,
    .el-pagination__jump {
      display: none !important;
    }
  }
}

// 表格响应式优化
.el-table {
  @include mobile-only {
    font-size: 14px;

    .el-table__header th,
    .el-table__body td {
      padding: 8px 4px;
    }

    // 移动端表格横向滚动
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

// 对话框响应式优化
.el-dialog {
  @include mobile-only {
    width: 95% !important;
    margin: 5vh auto !important;

    .el-dialog__header {
      padding: 15px 20px 10px;
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 15px;
    }
  }
}

// 抽屉响应式优化
.el-drawer {
  @include mobile-only {
    &.el-drawer__open .el-drawer {
      width: 100% !important;
    }
  }
}

// 菜单响应式优化
.el-menu {
  @include mobile-only {
    .el-menu-item,
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      padding: 0 16px;
    }
  }
}

// 表单响应式优化
.el-form {
  @include mobile-only {
    .el-form-item {
      margin-bottom: 18px;

      .el-form-item__label {
        line-height: 1.4;
        margin-bottom: 5px;
      }

      .el-form-item__content {
        line-height: 1.4;
      }
    }

    // 移动端表单项垂直布局
    &.el-form--label-top .el-form-item__label {
      text-align: left;
      padding: 0;
    }
  }
}

// 卡片响应式优化
.el-card {
  @include mobile-only {
    .el-card__header {
      padding: 15px 20px;
    }

    .el-card__body {
      padding: 20px;
    }
  }
}

// 步骤条响应式优化
.el-steps {
  @include mobile-only {
    .el-step__title {
      font-size: 14px;
      line-height: 1.4;
    }

    .el-step__description {
      font-size: 12px;
    }
  }
}

// 标签页响应式优化
.el-tabs {
  @include mobile-only {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        overflow-x: auto;

        .el-tabs__nav {
          white-space: nowrap;
        }
      }

      .el-tabs__item {
        padding: 0 15px;
        font-size: 14px;
      }
    }
  }
}
