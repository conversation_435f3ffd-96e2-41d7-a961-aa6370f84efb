/**
 * 响应式工具函数库
 * 提供各种响应式相关的工具函数和类
 */

import { BREAKPOINTS, DEVICE_BREAKPOINTS, MEDIA_QUERIES } from '@/constants/responsive'

/**
 * 响应式断点管理器
 */
export class ResponsiveManager {
  constructor() {
    this.listeners = new Map()
    this.currentBreakpoint = this.getCurrentBreakpoint()
    this.init()
  }

  /**
   * 初始化响应式管理器
   */
  init() {
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.handleResize.bind(this))
      this.handleResize() // 初始调用
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    const newBreakpoint = this.getCurrentBreakpoint()
    if (newBreakpoint !== this.currentBreakpoint) {
      const oldBreakpoint = this.currentBreakpoint
      this.currentBreakpoint = newBreakpoint
      this.notifyListeners(newBreakpoint, oldBreakpoint)
    }
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    if (typeof window === 'undefined') return 'lg'
    
    const width = window.innerWidth
    if (width < BREAKPOINTS.XS) return 'xs'
    if (width < BREAKPOINTS.SM) return 'sm'
    if (width < BREAKPOINTS.MD) return 'md'
    if (width < BREAKPOINTS.LG) return 'lg'
    if (width < BREAKPOINTS.XL) return 'xl'
    if (width < BREAKPOINTS.XXL) return 'xxl'
    return 'xxxl'
  }

  /**
   * 添加断点变化监听器
   */
  addListener(id, callback) {
    this.listeners.set(id, callback)
  }

  /**
   * 移除断点变化监听器
   */
  removeListener(id) {
    this.listeners.delete(id)
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(newBreakpoint, oldBreakpoint) {
    this.listeners.forEach(callback => {
      callback(newBreakpoint, oldBreakpoint)
    })
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', this.handleResize.bind(this))
    }
    this.listeners.clear()
  }
}

// 全局响应式管理器实例
export const responsiveManager = new ResponsiveManager()

/**
 * 响应式值计算器
 */
export class ResponsiveValueCalculator {
  /**
   * 根据断点计算值
   * @param {Object} values - 断点值映射
   * @param {string} currentBreakpoint - 当前断点
   * @returns {*} 计算后的值
   */
  static calculate(values, currentBreakpoint = null) {
    if (!currentBreakpoint) {
      currentBreakpoint = responsiveManager.getCurrentBreakpoint()
    }

    // 断点优先级（从大到小）
    const priorities = ['xxxl', 'xxl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = priorities.indexOf(currentBreakpoint)

    // 从当前断点开始向下查找可用值
    for (let i = currentIndex; i < priorities.length; i++) {
      const key = priorities[i]
      if (values[key] !== undefined) {
        return values[key]
      }
    }

    // 如果没有找到，返回默认值或第一个可用值
    return values.default || Object.values(values)[0]
  }

  /**
   * 插值计算（在两个断点之间进行线性插值）
   * @param {number} minValue - 最小值
   * @param {number} maxValue - 最大值
   * @param {number} minWidth - 最小宽度
   * @param {number} maxWidth - 最大宽度
   * @param {number} currentWidth - 当前宽度
   * @returns {number} 插值结果
   */
  static interpolate(minValue, maxValue, minWidth, maxWidth, currentWidth = null) {
    if (currentWidth === null) {
      currentWidth = typeof window !== 'undefined' ? window.innerWidth : maxWidth
    }

    if (currentWidth <= minWidth) return minValue
    if (currentWidth >= maxWidth) return maxValue

    const ratio = (currentWidth - minWidth) / (maxWidth - minWidth)
    return minValue + (maxValue - minValue) * ratio
  }
}

/**
 * CSS 类名生成器
 */
export class ResponsiveCSSGenerator {
  /**
   * 生成响应式类名
   * @param {string} baseClass - 基础类名
   * @param {Object} modifiers - 修饰符映射
   * @returns {string} 生成的类名
   */
  static generateClasses(baseClass, modifiers = {}) {
    const classes = [baseClass]
    const currentBreakpoint = responsiveManager.getCurrentBreakpoint()

    // 添加断点特定的类名
    if (modifiers[currentBreakpoint]) {
      classes.push(`${baseClass}--${modifiers[currentBreakpoint]}`)
    }

    // 添加设备类型类名
    const deviceType = this.getDeviceType(currentBreakpoint)
    classes.push(`${baseClass}--${deviceType}`)

    return classes.join(' ')
  }

  /**
   * 根据断点获取设备类型
   * @param {string} breakpoint - 断点
   * @returns {string} 设备类型
   */
  static getDeviceType(breakpoint) {
    if (['xs', 'sm'].includes(breakpoint)) return 'mobile'
    if (['md', 'lg'].includes(breakpoint)) return 'tablet'
    return 'desktop'
  }
}

/**
 * 响应式图片工具
 */
export class ResponsiveImageHelper {
  /**
   * 生成响应式图片 srcset
   * @param {string} basePath - 基础路径
   * @param {Array} sizes - 尺寸数组
   * @param {string} extension - 文件扩展名
   * @returns {string} srcset 字符串
   */
  static generateSrcSet(basePath, sizes, extension = 'jpg') {
    return sizes
      .map(size => `${basePath}_${size}w.${extension} ${size}w`)
      .join(', ')
  }

  /**
   * 生成响应式图片 sizes 属性
   * @param {Object} breakpointSizes - 断点尺寸映射
   * @returns {string} sizes 字符串
   */
  static generateSizes(breakpointSizes) {
    const mediaQueries = []

    Object.entries(breakpointSizes).forEach(([breakpoint, size]) => {
      if (BREAKPOINTS[breakpoint.toUpperCase()]) {
        const width = BREAKPOINTS[breakpoint.toUpperCase()]
        mediaQueries.push(`(min-width: ${width}px) ${size}`)
      }
    })

    // 添加默认尺寸
    mediaQueries.push('100vw')

    return mediaQueries.join(', ')
  }
}

/**
 * 响应式字体工具
 */
export class ResponsiveFontHelper {
  /**
   * 计算响应式字体大小
   * @param {Object} fontSizes - 字体大小映射
   * @param {boolean} useClamp - 是否使用 CSS clamp
   * @returns {string} CSS 字体大小值
   */
  static calculateFontSize(fontSizes, useClamp = true) {
    if (useClamp && typeof window !== 'undefined') {
      const minSize = fontSizes.xs || fontSizes.mobile || '14px'
      const maxSize = fontSizes.xxxl || fontSizes.desktop || '18px'
      const preferredSize = fontSizes.lg || fontSizes.tablet || '16px'

      return `clamp(${minSize}, ${preferredSize}, ${maxSize})`
    }

    return ResponsiveValueCalculator.calculate(fontSizes)
  }

  /**
   * 生成响应式行高
   * @param {number} fontSize - 字体大小（数值）
   * @param {number} ratio - 行高比例
   * @returns {string} 行高值
   */
  static calculateLineHeight(fontSize, ratio = 1.5) {
    return `${fontSize * ratio}px`
  }
}

/**
 * 响应式间距工具
 */
export class ResponsiveSpacingHelper {
  /**
   * 计算响应式间距
   * @param {Object} spacings - 间距映射
   * @param {string} property - CSS 属性名
   * @returns {Object} CSS 样式对象
   */
  static calculateSpacing(spacings, property = 'padding') {
    const currentSpacing = ResponsiveValueCalculator.calculate(spacings)
    return { [property]: currentSpacing }
  }

  /**
   * 生成响应式边距样式
   * @param {Object} margins - 边距配置
   * @returns {Object} CSS 样式对象
   */
  static generateMargins(margins) {
    const styles = {}
    
    Object.entries(margins).forEach(([direction, values]) => {
      const property = direction === 'all' ? 'margin' : `margin-${direction}`
      const value = ResponsiveValueCalculator.calculate(values)
      styles[property] = value
    })

    return styles
  }

  /**
   * 生成响应式内边距样式
   * @param {Object} paddings - 内边距配置
   * @returns {Object} CSS 样式对象
   */
  static generatePaddings(paddings) {
    const styles = {}
    
    Object.entries(paddings).forEach(([direction, values]) => {
      const property = direction === 'all' ? 'padding' : `padding-${direction}`
      const value = ResponsiveValueCalculator.calculate(values)
      styles[property] = value
    })

    return styles
  }
}

/**
 * 响应式布局工具
 */
export class ResponsiveLayoutHelper {
  /**
   * 生成响应式网格样式
   * @param {Object} gridConfig - 网格配置
   * @returns {Object} CSS 样式对象
   */
  static generateGridStyles(gridConfig) {
    const {
      columns = { xs: 1, sm: 2, md: 3, lg: 4 },
      gap = { xs: '1rem', sm: '1.5rem', md: '2rem' },
      minItemWidth = '250px'
    } = gridConfig

    const currentColumns = ResponsiveValueCalculator.calculate(columns)
    const currentGap = ResponsiveValueCalculator.calculate(gap)

    return {
      display: 'grid',
      gridTemplateColumns: minItemWidth 
        ? `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`
        : `repeat(${currentColumns}, 1fr)`,
      gap: currentGap
    }
  }

  /**
   * 生成响应式 Flexbox 样式
   * @param {Object} flexConfig - Flex 配置
   * @returns {Object} CSS 样式对象
   */
  static generateFlexStyles(flexConfig) {
    const {
      direction = { xs: 'column', md: 'row' },
      wrap = 'wrap',
      justify = 'flex-start',
      align = 'stretch',
      gap = { xs: '1rem', md: '2rem' }
    } = flexConfig

    const currentDirection = ResponsiveValueCalculator.calculate(direction)
    const currentGap = ResponsiveValueCalculator.calculate(gap)

    return {
      display: 'flex',
      flexDirection: currentDirection,
      flexWrap: wrap,
      justifyContent: justify,
      alignItems: align,
      gap: currentGap
    }
  }
}

// 导出工具函数
export const responsive = {
  manager: responsiveManager,
  calculate: ResponsiveValueCalculator.calculate,
  interpolate: ResponsiveValueCalculator.interpolate,
  generateClasses: ResponsiveCSSGenerator.generateClasses,
  generateSrcSet: ResponsiveImageHelper.generateSrcSet,
  generateSizes: ResponsiveImageHelper.generateSizes,
  calculateFontSize: ResponsiveFontHelper.calculateFontSize,
  calculateSpacing: ResponsiveSpacingHelper.calculateSpacing,
  generateGridStyles: ResponsiveLayoutHelper.generateGridStyles,
  generateFlexStyles: ResponsiveLayoutHelper.generateFlexStyles,
}

export default responsive
