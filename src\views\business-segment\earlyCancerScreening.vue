<!-- 
生命医疗 癌症早筛 页面 
-->
<template>
  <div class="business-details-main">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment/business-details' }">{{
            $t("businessSegment.scienceCenter") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("scienceCenter.biomedical") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("scienceCenter.earlyCancerScreening") }}</el-breadcrumb-item>

        </el-breadcrumb>
      </div>
    </div>

    <div class="content-main">
      <div class="content">
        <!-- 生命医疗 癌症早筛 -->
        <div class="img-container1">
          <el-image :src="EarlyCancerScreeninginfo.earlyCancerScreening_Img1" alt="癌症早筛"> </el-image>
          <div class="text-overlay">
            <div class="title">{{ EarlyCancerScreeninginfo[lang].sub_title1 }}</div>
            <div class="desc" v-html="EarlyCancerScreeninginfo[lang].sub_desc1"></div>
          </div>
        </div>
        <!-- 我们优势 -->
        <div
          style="text-align: center; font-size: 36px; line-height: 54px; font-weight: 600; margin-bottom: 120px; margin-top: 135px;">
          {{ EarlyCancerScreeninginfo[lang].sub_title2 }}
        </div>
        <div style="display: flex; flex-direction: column;">
          <!-- 图文展示区 -->
          <div>
            <div style="display: flex; flex-direction: row;">
              <div style="width: 50%;">
                <el-tabs v-model="activeName"  class="tabs-early-can" @tab-click="handleClick">
                  <el-tab-pane :label="EarlyCancerScreeninginfo[lang].sub_title3" name="first">
                    <li>{{ EarlyCancerScreeninginfo[lang].sub_desc3_l1 }}</li>
                    <li>{{ EarlyCancerScreeninginfo[lang].sub_desc3_l2 }}</li>
                    <li>{{ EarlyCancerScreeninginfo[lang].sub_desc3_l3 }}</li>
                  </el-tab-pane>
                  <el-tab-pane :label="EarlyCancerScreeninginfo[lang].sub_title4" name="second">
                    <li v-html="EarlyCancerScreeninginfo[lang].sub_desc4_l1"></li>
                    <li v-html="EarlyCancerScreeninginfo[lang].sub_desc4_l2"></li>
                    <li v-html="EarlyCancerScreeninginfo[lang].sub_desc4_l3"></li>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <div>
                <el-image v-if="activeName == 'first'" :src="EarlyCancerScreeninginfo.earlyCancerScreening_Img2"></el-image>
                <el-image v-if="activeName == 'second'" :src="EarlyCancerScreeninginfo.earlyCancerScreening_Img3"></el-image>
              </div>
            </div> 
          </div>

          <!-- 箭头按钮 -->
          <div style="margin: 0 auto;">
            <div style="margin: 47px auto 232px auto;">
              <span style="padding: 0 32px;" @click="btnClick('l')"><el-icon>
                  <ArrowLeftBold />
                </el-icon></span>
              <span style="padding: 0 32px;"@click="btnClick('r')"><el-icon>
                  <ArrowRightBold />
                </el-icon></span>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </div>
</template>

<script  lang="ts" setup>
import { nextTick, ref } from "vue"
import { ArrowLeftBold, ArrowRight, ArrowRightBold } from "@element-plus/icons-vue"
import { onBeforeMount, reactive } from "vue"

import earlyCancerScreening_info from "./earlyCancerScreening-info"


import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const router = useRouter()
const EarlyCancerScreeninginfo = reactive(earlyCancerScreening_info)
const activeName = ref('first')

const btnClick = (item) => {
  if (activeName.value == 'first') {
    activeName.value = 'second'
  }
  else {
    activeName.value = 'first'
  }
  nextTick.apply();
}

const lang = ref(i18n.global.locale)

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  },
  () => activeName,
  
)

const imgClick = (path) =>{
  router.push({
    path: path,
    query: {
      // key: item.key
    }
  })
}
onBeforeMount(() => {
  console.log(earlyCancerScreening_info)
})
</script>

<style lang="scss" scoped>
 
.business-details-main {
  //background: #ffffff;
 
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  max-width: 1920px;
  width: 100vw;
  margin: 0 auto;
  .sub-title {
    height: 100px;
    background: #f3f3f3;

    .top-breadcrumb {
      width: 1320px;
      max-width: 100%;
      margin: 0 auto;
      padding-top: 50px;

      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        font-size: 18px;

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {
    width: 80%;
    margin: 0 auto;
    padding: 50px 0;
    background-size: cover;

    .info {
      .title {
        margin-left: 10%;
        width: 100%;
        font-weight: 600;
        font-size: 26px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 2px;
      }

      .desc {
        margin: 10px 10%;
        font-size: 20px;
        color: #000000;
        line-height: 28px;
        letter-spacing: 2px;
      }
    }
    .img-container1 {
      position: relative;
      display: flex;
      /* 启用flex布局 */
      justify-content: center;
      /* 水平居中 */
      align-items: start;
      /* 垂直居中 */
      width: 100%;
      color: black;
    }
  
    .text-overlay {
      position: absolute;
      z-index: 1;
      /* 确保文字在图片上方 */
      color: black;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      /* 文字阴影增强可读性 */
      margin: 10% 20%;
  
      .title {
        font-size: 48px;
      }
  
      .desc {
        margin-top: 28px;
        line-height: 32px;
        font-size: 24px;
        width: 100%;
        letter-spacing: 4px;
      }
    }
    .img-view {
      margin: 10px 0;
      width: 100%;
      height: auto;

      .img {
        display: block;
        width: 100%;
        height: auto;
      }
    }
    .sub-tag {
      margin: 10px 0;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
    .sub-tag1 {
      margin-top: 18px;
      color: blue;
      font-size: 24px;
      font-weight: 600;
    }
    .sub-tag2 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 20px;
    }
    .imgpos {
      vertical-align: middle;
    }
  }
 
  
  .tabs-early-can {
    :deep(.el-tabs__item.is-active, .el-tabs__item:hover) {
      color: black;
    }
    :deep(.el-tabs__item) {
      font-size: 36px;
      line-height: 54px;
      margin-bottom: 20px;
    }
    li {
      margin-top: 3rem;
    }
  }
   
    
  
  
}


@media (max-width: 1320px) {
  .business-details-main .sub-title .top-breadcrumb,
  .business-details-main .content {
    padding-left: 20px;
    padding-right: 20px;
  }

  .business-details-main .content .img-view {
    width: 100%;
    height: auto;
  }

  .business-details-main .research-institute {
    padding-left: 20px;
    padding-right: 20px;
    gap: 100px;
  }

  .business-details-main .research-institute .energy-security {
    flex-direction: column-reverse;
    gap: 30px;
    align-items: flex-start;
  }

  .business-details-main .research-institute .energy-security .left {
    padding: 0;
  }

  .business-details-main .research-institute .energy-security .right {
    left: 0;
    width: 100%;
    height: 350px;
  }

  .business-details-main .research-institute .energy-security:hover .left {
    background: initial;

    .title,
    .desc,
    .more-btn {
      color: initial;
    }

    .line {
      border: 1px solid #979797;
    }
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 34px;
  }
}

@media (max-width: 1024px) {
  .business-details-main .content .info .title {
    font-size: 28px;
    line-height: 38px;
    min-width: 300px;
  }
  .business-details-main .content .info .desc {
    font-size: 18px;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 32px;
  }
}

@media (max-width: 820px) {
  .business-details-main .content .info {
    flex-direction: column;
    gap: 30px;
  }

  .business-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
  }

  .business-details-main .content .img-view {
    margin-top: 20px;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 30px;
  }
  .business-details-main .research-institute .energy-security .left .line {
    margin-top: 30px;
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .business-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
    line-height: 14px;
  }
}

@media (max-width: 576px) {
  .business-details-main .content .info .title {
    font-size: 24px;
    line-height: 34px;
  }

  .business-details-main .content .info .desc {
    font-size: 16px;
  }

  .business-details-main .content {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .business-details-main .research-institute .energy-security .right {
    width: 100%;
    min-width: 100%;
    height: auto;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 26px;
  }
  .business-details-main .research-institute .energy-security .left .desc {
    font-size: 16px;
  }
  .business-details-main .research-institute .energy-security .left .more-btn {
    margin-top: 30px;
  }
  .business-details-main .content .img-container1 {
    margin: 5px 10px;
    display: grid;
    grid-template-columns: 90%;
  }
  .business-details-main .content .img-container1 .img-view {
    margin: 5px 10px;
  }
  .business-details-main .content .img-container1 .sub-tag1 {
    text-align: center;
  }
  .business-details-main .content .img-container1 .sub-tag2 {
    text-align: center;
  }
}

@media (max-width: 480px) {
  :deep(.el-carousel__arrow--right) {
    top: 13%;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 24px;
  }
}

@media (max-width: 390px) {
  .business-details-main .content .info .title {
    font-size: 22px;
  }

  :deep(.el-carousel__indicators--horizontal) {
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
