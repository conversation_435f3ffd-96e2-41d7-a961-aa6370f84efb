/// <reference types="vitest" />

import { loadEnv } from "vite"
import path, { resolve } from "path"
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import svgLoader from "vite-svg-loader"

import { terser } from "rollup-plugin-terser"
import viteCompression from "vite-plugin-compression"

import { createFilter } from "@rollup/pluginutils"

/** 配置项文档：https://cn.vitejs.dev/config */
export default ({ mode }) => {
  const viteEnv = loadEnv(mode, process.cwd())
  const { VITE_PUBLIC_PATH } = viteEnv
  return {
    /** 打包时根据实际情况修改 base */
    base: VITE_PUBLIC_PATH,
    resolve: {
      alias: {
        /** @ 符号指向 src 目录 */
        "@": resolve(__dirname, "./src")
      }
    },
    server: {
      /** 设置 host: true 才可以使用 Network 的形式，以 IP 访问项目 */
      host: true, // host: "0.0.0.0"
      /** 端口号 */
      port: 3005,
      /** 是否自动打开浏览器 */
      open: false,
      /** 跨域设置允许 */
      cors: true,
      /** 端口被占用时，是否直接退出 */
      strictPort: false,
      /** 接口代理 */
      proxy: {
        // "/api": {
        //   target: "http://localhost:3050",
        //   ws: true,
        //   /** 是否允许跨域 */
        //   changeOrigin: true
        // }
      },
      /** 预热常用文件，提高初始页面加载速度 */
      warmup: {
        clientFiles: ["./src/layouts/**/*.vue"]
      }
    },
    build: {
      /** 单个 chunk 文件的大小超过 2048KB 时发出警告 */
      chunkSizeWarningLimit: 2048,
      /** 禁用 gzip 压缩大小报告 */
      reportCompressedSize: false,
      /** 打包后静态资源目录 */
      assetsDir: "static",
      rollupOptions: {
        output: {
          /**
           * 分块策略
           * 1. 注意这些包名必须存在，否则打包会报错
           * 2. 如果你不想自定义 chunk 分割策略，可以直接移除这段配置
           */
          manualChunks: {
            vue: ["vue", "vue-router", "pinia"],
            element: ["element-plus", "@element-plus/icons-vue"],
            vxe: ["xe-utils"]
          }
        },
        plugins: [
          // 压缩代码 先 esbuild 快速压缩 再 terser 压缩率高
          terser({
            compress: {
              drop_console: true, // 删除 console.log
              drop_debugger: true, // 删除 debugger
              dead_code: true, // 删除无用的代码
              unused: true // 删除未使用的变量或函数
            },
            format: {
              comments: false // 删除所有注释
            },
            toplevel: true, // 删除无用代码
            mangle: true // 短化变量名
          })
        ]
      }
    },
    // esbuild 压缩速度块 但是 terser 压缩率高
    /** 混淆器 */
    esbuild:
      mode === "development"
        ? undefined
        : {
            /** 打包时移除 console.log */
            pure: ["console.log"],
            /** 打包时移除 debugger */
            drop: ["debugger"],
            /** 打包时移除所有注释 */
            legalComments: "none"
          },
    /** Vite 插件 */
    plugins: [
      {
        name: "skip-video-files",
        generateBundle(options, bundle) {
          const filter = createFilter(/\.(mp4|webm|ogg|mov)$/i)
          for (const name in bundle) {
            if (filter(name)) {
              delete bundle[name]
            }
          }
        }
      },
      viteCompression({
        filter: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i, // 需要压缩的文件
        threshold: 600, // 文件容量大于这个值进行压缩
        algorithm: "gzip", // 压缩方式
        ext: "gz", // 后缀名
        level: 9, // 设置压缩级别为6
        deleteOriginFile: false // 压缩后是否删除压缩源文件
      }),
      vue(),
      vueJsx(),
      /** 将 SVG 静态图转化为 Vue 组件 */
      svgLoader({ defaultImport: "url" }),
      /** SVG */
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/icons/svg")],
        symbolId: "icon-[dir]-[name]"
      })
    ],
    /** Vitest 单元测试配置：https://cn.vitest.dev/config */
    test: {
      include: ["tests/**/*.test.ts"],
      environment: "jsdom"
    }
  }
}
