/*
 * @Author: <PERSON>
 * @Date: 2024-05-10 11:53:32
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-08-01 11:33:06
 */

import { createI18n } from "vue-i18n"
import { messagesEn } from "./en"
import { messagesZh } from "./zh"
import { messagesHK } from "./zh-hk"

import { getI18nLocale } from "@/utils/cache/local-storage"

export const i18n = createI18n({
  locale: getI18nLocale() || "zh",
  messages: {
    en: messagesEn,
    zh: messagesZh,
    zh_hk: messagesHK
  },
  warnHtmlInMessage: "off" // 关闭警告
})
