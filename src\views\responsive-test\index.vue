<!--
 * 响应式测试页面
 * 用于测试和验证响应式功能的效果
 -->
<template>
  <div class="responsive-test app-container">
    <h1 class="test-title">响应式功能测试页面</h1>
    
    <!-- 当前断点信息 -->
    <div class="breakpoint-info">
      <h2>当前断点信息</h2>
      <div class="info-grid">
        <div class="info-item">
          <label>窗口宽度:</label>
          <span>{{ windowWidth }}px</span>
        </div>
        <div class="info-item">
          <label>当前断点:</label>
          <span>{{ currentBreakpoint }}</span>
        </div>
        <div class="info-item">
          <label>设备类型:</label>
          <span>{{ deviceType }}</span>
        </div>
        <div class="info-item">
          <label>是否移动端:</label>
          <span>{{ isMobile ? '是' : '否' }}</span>
        </div>
      </div>
    </div>

    <!-- 响应式容器测试 -->
    <section class="test-section">
      <h2>响应式容器测试</h2>
      <ResponsiveContainer class="test-container">
        <div class="container-content">
          <p>这是一个响应式容器，会根据屏幕大小自动调整最大宽度和内边距。</p>
          <p>移动端: 最大宽度100%, 内边距1rem</p>
          <p>平板端: 最大宽度768px, 内边距2rem</p>
          <p>桌面端: 最大宽度1200px, 内边距4rem</p>
        </div>
      </ResponsiveContainer>
    </section>

    <!-- 响应式网格测试 -->
    <section class="test-section">
      <h2>响应式网格测试</h2>
      <ResponsiveGrid 
        :cols="{ xs: 1, sm: 2, md: 3, lg: 4 }"
        :gap="{ xs: '1rem', md: '1.5rem', lg: '2rem' }"
        class="test-grid"
      >
        <div v-for="i in 8" :key="i" class="grid-item">
          <div class="grid-content">
            <h3>网格项 {{ i }}</h3>
            <p>这是第{{ i }}个网格项</p>
          </div>
        </div>
      </ResponsiveGrid>
    </section>

    <!-- 响应式指令测试 -->
    <section class="test-section">
      <h2>响应式指令测试</h2>
      
      <!-- 响应式显示/隐藏 -->
      <div class="directive-test">
        <h3>响应式显示/隐藏</h3>
        <div 
          v-responsive:show="{ mobile: true, desktop: false }"
          class="test-box mobile-only"
        >
          只在移动端显示
        </div>
        <div 
          v-responsive:show="{ mobile: false, desktop: true }"
          class="test-box desktop-only"
        >
          只在桌面端显示
        </div>
      </div>

      <!-- 响应式类名 -->
      <div class="directive-test">
        <h3>响应式类名</h3>
        <div 
          v-responsive:class="{ 
            mobile: 'mobile-style', 
            tablet: 'tablet-style', 
            desktop: 'desktop-style' 
          }"
          class="test-box responsive-class"
        >
          根据设备类型应用不同的类名
        </div>
      </div>

      <!-- 响应式样式 -->
      <div class="directive-test">
        <h3>响应式样式</h3>
        <div 
          v-responsive:style="{ 
            mobile: { backgroundColor: '#ff6b6b', padding: '10px' },
            tablet: { backgroundColor: '#4ecdc4', padding: '20px' },
            desktop: { backgroundColor: '#45b7d1', padding: '30px' }
          }"
          class="test-box responsive-style"
        >
          根据设备类型应用不同的样式
        </div>
      </div>
    </section>

    <!-- 响应式字体测试 -->
    <section class="test-section">
      <h2>响应式字体测试</h2>
      <div class="font-test">
        <h3 class="responsive-heading">响应式标题</h3>
        <p class="responsive-text">这是响应式文本，字体大小会根据屏幕大小自动调整。</p>
      </div>
    </section>

    <!-- 响应式间距测试 -->
    <section class="test-section">
      <h2>响应式间距测试</h2>
      <div class="spacing-test">
        <div class="spacing-item">项目 1</div>
        <div class="spacing-item">项目 2</div>
        <div class="spacing-item">项目 3</div>
      </div>
    </section>

    <!-- 断点测试指示器 -->
    <div class="breakpoint-indicators">
      <div class="indicator xs" :class="{ active: currentBreakpoint === 'xs' }">XS</div>
      <div class="indicator sm" :class="{ active: currentBreakpoint === 'sm' }">SM</div>
      <div class="indicator md" :class="{ active: currentBreakpoint === 'md' }">MD</div>
      <div class="indicator lg" :class="{ active: currentBreakpoint === 'lg' }">LG</div>
      <div class="indicator xl" :class="{ active: currentBreakpoint === 'xl' }">XL</div>
      <div class="indicator xxl" :class="{ active: currentBreakpoint === 'xxl' }">XXL</div>
    </div>
  </div>
</template>

<script setup>
import { useBreakpoints } from '@/hooks/useResponsive'
import ResponsiveContainer from '@/components/ResponsiveContainer/index.vue'
import ResponsiveGrid from '@/components/ResponsiveGrid/index.vue'

// 使用响应式断点 hook
const { 
  windowWidth, 
  currentBreakpoint, 
  deviceType, 
  isMobile, 
  isTablet, 
  isDesktop 
} = useBreakpoints()
</script>

<style lang="scss" scoped>
.responsive-test {
  min-height: 100vh;
  padding: 2rem 0;
}

.test-title {
  @include responsive-font-size(24px, 32px, 40px);
  text-align: center;
  margin-bottom: 2rem;
  color: var(--el-color-primary);
}

.breakpoint-info {
  @include responsive-card;
  margin-bottom: 2rem;
  
  h2 {
    @include responsive-font-size(18px, 22px, 24px);
    margin-bottom: 1rem;
    color: var(--el-text-color-primary);
  }
  
  .info-grid {
    @include responsive-grid(1, 2, 4);
    
    .info-item {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      
      label {
        font-weight: 600;
        color: var(--el-text-color-regular);
      }
      
      span {
        font-size: 1.2em;
        color: var(--el-color-primary);
        font-weight: 700;
      }
    }
  }
}

.test-section {
  margin-bottom: 3rem;
  
  h2 {
    @include responsive-font-size(20px, 24px, 28px);
    margin-bottom: 1.5rem;
    color: var(--el-text-color-primary);
    border-bottom: 2px solid var(--el-color-primary);
    padding-bottom: 0.5rem;
  }
}

.test-container {
  border: 2px dashed var(--el-color-primary);
  
  .container-content {
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 1rem;
    
    p {
      margin-bottom: 0.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.test-grid {
  .grid-item {
    .grid-content {
      @include responsive-card;
      text-align: center;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      h3 {
        @include responsive-font-size(16px, 18px, 20px);
        margin-bottom: 0.5rem;
        color: var(--el-color-primary);
      }
      
      p {
        color: var(--el-text-color-regular);
      }
    }
  }
}

.directive-test {
  margin-bottom: 2rem;
  
  h3 {
    @include responsive-font-size(16px, 18px, 20px);
    margin-bottom: 1rem;
    color: var(--el-text-color-primary);
  }
  
  .test-box {
    @include responsive-card;
    text-align: center;
    font-weight: 600;
    margin-bottom: 1rem;
    
    &.mobile-only {
      background: #ff6b6b;
      color: white;
    }
    
    &.desktop-only {
      background: #45b7d1;
      color: white;
    }
    
    &.mobile-style {
      background: #ff6b6b;
      color: white;
      border-radius: 20px;
    }
    
    &.tablet-style {
      background: #4ecdc4;
      color: white;
      border-radius: 10px;
    }
    
    &.desktop-style {
      background: #45b7d1;
      color: white;
      border-radius: 5px;
    }
  }
}

.font-test {
  .responsive-heading {
    @include responsive-font-size(20px, 28px, 36px);
    margin-bottom: 1rem;
    color: var(--el-color-primary);
  }
  
  .responsive-text {
    @include responsive-font-size(14px, 16px, 18px);
    line-height: 1.6;
    color: var(--el-text-color-regular);
  }
}

.spacing-test {
  @include responsive-flex(column, row, row);
  @include responsive-spacing(gap, 1rem, 1.5rem, 2rem);
  
  .spacing-item {
    @include responsive-card;
    flex: 1;
    text-align: center;
    background: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    font-weight: 600;
  }
}

.breakpoint-indicators {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 5px;
  z-index: 1000;
  
  .indicator {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--el-bg-color);
    border: 2px solid var(--el-border-color);
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    color: var(--el-text-color-regular);
    transition: all 0.3s ease;
    
    &.active {
      background: var(--el-color-primary);
      color: white;
      border-color: var(--el-color-primary);
      transform: scale(1.2);
    }
  }
}
</style>
