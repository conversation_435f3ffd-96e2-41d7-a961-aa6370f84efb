<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-04 17:48:32
-->

<template>
  <div class="contact-us-main">
    <div class="background">
      <div class="img">
        <h2 class="title">{{ $t("contactUs.title") }}</h2>
      </div>
    </div>

    <div class="company-information">
      <div class="content">
        <div class="sticky-header-main">
          <div class="sticky-header">
            <div class="card">
              <div class="title">
                <div :class="{ active: activeContactDom == 'part1' }" @click="gotoDomView('part1')">
                  {{ $t("home.scientificResearchSection") }}
                </div>
                <div :class="{ active: activeContactDom == 'part2' }" @click="gotoDomView('part2')">
                  {{ $t("contactUs.subTitle2") }}
                </div>
                <div :class="{ active: activeContactDom == 'part3' }" @click="gotoDomView('part3')">
                  {{ $t("contactUs.subTitle3") }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="cards-main-content">
          <div class="cards-main">
            <div class="cards-part1" id="part1">
              <h2>{{ $t("home.scientificResearchSection") }}</h2>
              <div class="item-list">
                <div>{{ $t("contactUs.subTitle1_desc1") }}</div>
                <div>{{ $t("contactUs.subTitle1_desc2") }}</div>
                <div>{{ $t("contactUs.subTitle1_desc3") }}</div>
                <div>{{ $t("contactUs.subTitle1_desc4") }}</div>
                <div>{{ $t("contactUs.subTitle1_desc5") }}</div>
              </div>
            </div>
            <div class="cards-part2" id="part2">
              <h2>
                {{ $t("contactUs.subTitle2") }} <span>{{ $t("contactUs.subTitle2_tip") }} </span>
              </h2>
              <div class="item-list">
                <div class="item">
                  <h3 class="address">{{ $t("contactUs.area1") }}</h3>
                  <div class="desc">
                    <!-- <p class="company">{{ $t("contactUs.company1") }}</p> -->
                    <p>{{ $t("contactUs.address1") }}</p>
                    <p>{{ $t("contactUs.phone") }}：+86 0755-27211126</p>
                    <p>{{ $t("contactUs.email") }}：<EMAIL></p>
                    <p>{{ $t("contactUs.zipCode") }}：518100</p>
                  </div>
                </div>
                <div class="item">
                  <h3 class="address">{{ $t("contactUs.area_hk") }}</h3>
                  <div class="desc">
                    <!-- <p class="company">{{ $t("contactUs.company_hk") }}</p> -->
                    <p>{{ $t("contactUs.address_hk") }}</p>
                    <p>{{ $t("contactUs.phone") }}：+852-9306 3628</p>
                    <!-- <p>{{ $t("contactUs.email") }}：<EMAIL></p> -->
                  </div>
                </div>
              </div>
            </div>
            <div class="cards-part3" id="part3">
              <h2>{{ $t("contactUs.subTitle3") }}</h2>
              <div class="item-list">
                <div>{{ $t("contactUs.subTitle3_desc1") }}</div>
                <div>{{ $t("contactUs.subTitle3_desc2") }}</div>
                <div>{{ $t("contactUs.subTitle3_desc3") }}</div>
                <div>{{ $t("contactUs.subTitle3_desc4") }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onBeforeMount, onMounted, onBeforeUnmount, ref, watch } from "vue"
import { useRouter } from "vue-router"

const router = useRouter()

const activeContactDom = ref("part1")

const gotoDomView = (id) => {
  localStorage.setItem("activeContactDom", id)
  scrollPage(id)
}

const scrollPage = (part) => {
  if (part) {
    activeContactDom.value = part

    const appScrollbar = document.getElementsByClassName("app-scrollbar")[0]
    appScrollbar.removeEventListener("scroll", () => {})

    const dom = document.getElementById(part)
    dom.scrollIntoView({ behavior: "smooth" })
  }
}

watch(
  () => router.currentRoute.value.query?.part,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      scrollPage(newVal)
    }
  }
)

onBeforeMount(() => {
  const part = localStorage.getItem("activeContactDom")
  if (part) {
    router.push({ query: { part: part } })
  }
})

onMounted(() => {
  scrollPage(router.currentRoute.value.query?.part)

  localStorage.setItem("activeContactDom", activeContactDom.value)
})

onBeforeUnmount(() => {
  const part = localStorage.getItem("activeContactDom")
  if (part) {
    localStorage.removeItem("activeContactDom")
  }
})
</script>

<style lang="scss" scoped>
.contact-us-main {
  .background {
    image-rendering: -webkit-optimize-contrast;
    position: relative;

    .img {
      width: 100%;
      height: 489px;
      background: url("@/assets/contact-us/contact-us.jpg") no-repeat center center;
      background-size: cover;
    }
    .title {
      font-size: 35px;
      letter-spacing: 5px;

      max-width: 1320px;
      width: 100%;
      margin: 0 auto;

      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);

      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    }
  }

  .company-information {
    background: #f3f3f3;

    .content {
      .sticky-header-main {
        background-color: #f3f3f3;
      }

      .sticky-header {
        max-width: 1320px;
        width: 100%;
        margin: 0 auto;

        position: sticky;
        top: 0px;
        z-index: 1000;

        overflow: hidden;

        .card {
          max-width: 1320px;
          width: 100%;
          margin: 0 auto;
          font-size: 18px;
          padding-top: 50px;
          padding-bottom: 15px;
        }

        .title {
          display: flex;
          justify-content: flex-start;
          gap: 75px;

          div {
            position: relative;
            cursor: pointer;
            padding-bottom: 8px;
          }

          .active {
            font-weight: 700;
            border-radius: 2px;

            transition: all 0.3s;

            &::before {
              height: 4px;
              content: "";
              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              background: #1c2a77;
              border-radius: 2px;
              transition: move 0.3s ease;

              @keyframes move {
                0% {
                  transform: translateX(-20px);
                  opacity: 0;
                }
                100% {
                  transform: translateX(0);
                  opacity: 1;
                }
              }
            }
          }
        }
      }

      .cards-main-content {
        background: #ffffff;

        .cards-main {
          padding: 100px 0;
          max-width: 1320px;
          width: 100%;
          margin: 0 auto;

          display: flex;
          flex-direction: column;
          gap: 120px;

          h2 {
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 600;
            font-size: 36px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 54px;
            letter-spacing: 3px;
            font-style: normal;
            border-bottom: 1px solid #979797;
            padding-bottom: 10px;
          }

          .cards-part1 {
            .item-list {
              padding-top: 60px;
              display: flex;
              flex-wrap: wrap;
              row-gap: 60px;

              div {
                flex: 0 0 calc(100% / 3);
                max-width: calc(100% / 3);
                font-size: 24px;
                font-weight: 700;
                color: rgba(0, 0, 0, 0.85);
                letter-spacing: 1px;
                line-height: 30px;
              }
            }
          }

          .cards-part2 {
            h2 {
              span {
                font-size: 18px;
              }
            }

            .item-list {
              display: flex;
              gap: 135px;
              margin-top: 60px;
            }

            .item {
              .address {
                max-width: 340px;

                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 800;
                font-size: 24px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 36px;
                letter-spacing: 4px;
                text-align: left;
                font-style: normal;

                padding-bottom: 10px;
                border-bottom: 1px solid #979797;
              }

              .desc {
                padding-top: 44px;

                .company {
                  font-weight: 700;
                  font-size: 18px;
                  margin-bottom: 10px;
                }

                p {
                  font-family: SourceHanSansCN, SourceHanSansCN;
                  font-weight: 400;
                  font-size: 16px;
                  color: #000000;
                  line-height: 28px;
                  font-style: normal;
                }
              }
            }
          }

          .cards-part3 {
            .item-list {
              margin-top: 60px;
              display: flex;
              flex-direction: column;
              gap: 60px;

              div {
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 800;
                font-size: 24px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 36px;
                letter-spacing: 4px;
                text-align: left;
                font-style: normal;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 1320px) {
  .contact-us-main .company-information .content .sticky-header .card,
  .contact-us-main .company-information .content .cards-main-content {
    padding-left: 20px;
    padding-right: 20px;
  }
}
@media (max-width: 1024px) {
  .contact-us-main .background .title {
    padding: 0 20px;
  }
}
@media (max-width: 820px) {
  .contact-us-main .background .img {
    height: 300px;
  }
  .contact-us-main .background .title {
    font-size: 28px;
  }
  .contact-us-main .company-information .content {
    gap: 80px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part1 .item-list {
    flex-direction: column;

    div {
      flex: 1;
      max-width: 100%;
    }
  }
}
@media (max-width: 768px) {
}
@media (max-width: 576px) {
  .contact-us-main .company-information .content .cards-main-content .cards-main h2 {
    font-size: 30px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part1 .item-list div,
  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part3 .item-list div {
    font-size: 22px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part2 .item-list {
    flex-direction: column;
  }
}
@media (max-width: 480px) {
  .contact-us-main .background .title {
    font-size: 24px;
  }

  .contact-us-main .company-information .content {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .contact-us-main .company-information .content {
    padding-top: 0;
    padding-bottom: 0;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main {
    padding-top: 40px;
    padding-bottom: 60px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main h2 {
    font-size: 24px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part1 .item-list div,
  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part3 .item-list div {
    font-size: 18px;
  }

  .contact-us-main .company-information .content .sticky-header .card {
    font-size: 16px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part1 .item-list,
  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part3 .item-list {
    row-gap: 30px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part2 .item-list,
  .contact-us-main .company-information .content .cards-main-content .cards-main {
    gap: 90px;
  }
}
@media (max-width: 390px) {
  .contact-us-main .company-information .content .item .desc .company {
    font-size: 16px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main h2 {
    font-size: 20px;
  }

  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part1 .item-list div,
  .contact-us-main .company-information .content .cards-main-content .cards-main .cards-part3 .item-list div {
    font-size: 16px;
  }
}
@media (max-width: 380px) {
  .contact-us-main .company-information .content .item .desc .company {
    font-size: 15px;
  }
  .contact-us-main .company-information .content .item .desc p {
    font-size: 15px;
  }

  .contact-us-main .company-information .content .item .address {
    font-size: 22px;
  }
}
</style>
