<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-20 14:51:55
-->
<template>
  <div class="layout-logo-container" :class="{ collapse: props.collapse, 'layout-mode-top': isTop }">
    <transition name="layout-logo-fade">
      <router-link class="logo-link-collapse" v-if="props.collapse" key="collapse" to="/">
        <!-- 折叠时 -->
        <img :src="logo" class="layout-logo" />
      </router-link>
      <router-link class="logo-link" :class="{ logoIsMobile: props.isMobile }" v-else key="expand" to="/">
        <!-- 反之 -->
        <img :src="logoText2" class="layout-logo-text" />
        <div>
          <img :src="rightImgMap[lang]" class="layout-logo-right" />
        </div>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import { ref, watch } from "vue"

import { useLayoutMode } from "@/hooks/useLayoutMode"

import logo from "@/assets/layouts/logo.webp?url"
import logoText2 from "@/assets/layouts/logo-text-2.webp?url"
import logoRight from "@/assets/layouts/logo-right.webp"
import logoRight_en from "@/assets/layouts/logo-right-en.webp"

import { i18n } from "@/i18n"

const lang = ref(i18n.global.locale)

const rightImgMap = {
  zh: logoRight,
  en: logoRight_en,
  zh_hk: logoRight
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const props = defineProps({
  collapse: Boolean, // 是否折叠
  isMobile: Boolean // 是否移动端
})

const { isLeft, isTop } = useLayoutMode()
</script>

<style lang="scss" scoped>
.layout-logo-container {
  position: relative;
  // width: 100%;
  // height: var(--v3-header-height);
  // line-height: var(--v3-header-height);
  text-align: center;
  overflow: hidden;
  .layout-logo {
    display: none;
  }
  .layout-logo-text {
    vertical-align: middle;

    height: 80px;
  }
}

.layout-mode-top {
  // height: var(--v3-navigationbar-height);
  // line-height: var(--v3-navigationbar-height);

  display: flex;
  align-items: center;
  justify-content: center;

  .logo-link {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    gap: 20px;

    .layout-logo-text {
      width: 65px;
      height: auto;
    }

    .layout-logo-right {
      max-width: 180px;
    }
  }

  .logoIsMobile {
    gap: 20px;

    .layout-logo-text {
      width: 60px;
      height: auto;
    }

    .layout-logo-right {
      width: 190px;
    }
  }
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }
  .layout-logo-text {
    display: none;
  }
}
</style>
