<template>
  <div class="three-core">
    <div class="three-core-main">
      <h2 class="title">{{ titleType == "home" ? $t("home.threeCoreTitle") : $t("businessSegment.title") }}</h2>
      <div class="info-view">
        <div class="left">
          <el-image class="img" :src="companyProfile" fit="cover" lazy />
        </div>
        <div class="right" @click="gotoHKSciences()">
          <h3 class="science-title">{{ $t("home.scienceTitle") }}</h3>
          <p class="desc">{{ $t("home.scienceInfo") }}</p>
          <p class="desc">{{ $t("home.researchScope") }}</p>
          <p class="desc">{{ $t("home.scienceVision") }}</p>
          <div class="more-btn">
            <div class="more-view">
              {{ $t("public.learnMore")
              }}<span class="icon"
                ><el-icon><Right /></el-icon
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// import companyProfile from "@/assets/home/<USER>"
import companyProfile from "@/assets/home/<USER>"
import { useRouter } from "vue-router"
const router = useRouter()
const props = defineProps({
  titleType: String
})

const gotoHKSciences = () => {
  router.push("/business-segment/business-details")
}
</script>

<style lang="scss" scoped>
.three-core {
  .three-core-main {
    @include responsive-container;
    @include responsive-spacing(padding-top, 60px, 80px, 120px);

    .title {
      @include responsive-font-size(28px, 36px, 40px);
      color: rgba(0, 0, 0);
      text-align: center;
      @include responsive-spacing(margin-bottom, 30px, 45px, 60px);
    }

    .info-view {
      @include responsive-flex(column, row, row);
      align-items: center;
      @include responsive-spacing(gap, 20px, 30px, 0);

      &:hover {
        .right {
          background: #409eff;

          .science-title,
          .desc,
          .more-btn {
            color: #ffffff;
          }
        }
      }

      .left {
        position: relative;
        z-index: 20;
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);

        // 响应式尺寸
        @include mobile-only {
          width: 100%;
          height: 250px;
        }

        @include tablet-only {
          width: 100%;
          height: 350px;
        }

        @include desktop-only {
          width: 800px;
          height: 450px;
        }

        .img {
          width: 100%;
          height: 100%;
          @include responsive-image;
        }
      }

      .right {
        position: relative;
        z-index: 10;
        background: #f3f3f3;
        cursor: pointer;

        // 响应式布局
        @include mobile-only {
          width: 100%;
          padding: 30px 20px;
          left: 0;
        }

        @include tablet-only {
          width: 100%;
          padding: 50px 40px;
          left: 0;
        }

        @include desktop-only {
          width: 760px;
          padding: 100px 100px 100px 210px;
          left: -100px;
        }

        .science-title {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 25px;
        }
        .desc {
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
          text-align: left;
          letter-spacing: 2px;
        }

        .more-btn {
          display: flex;
          align-items: center;
          margin-top: 40px;
          font-size: 16px;
          font-weight: 600;

          .more-view {
            display: flex;
            align-items: center;
          }
        }

        .icon {
          padding-left: 10px;
          font-size: 20px;

          display: flex;
          align-items: center;
        }
      }
    }
  }
}

@import "./media.scss";
</style>
