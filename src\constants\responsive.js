/**
 * 响应式断点常量配置
 * 统一管理项目中所有的响应式断点，确保 CSS、JavaScript 和组件库使用一致的断点
 */

// 基础断点定义 (px)
export const BREAKPOINTS = {
  XS: 480,      // 超小屏幕 (手机横屏)
  SM: 640,      // 小屏幕 (平板竖屏)
  MD: 768,      // 中等屏幕 (平板横屏)
  LG: 992,      // 大屏幕 (桌面) - 与 Bootstrap 保持一致
  XL: 1200,     // 超大屏幕 (大桌面)
  XXL: 1320,    // 超超大屏幕 (超大桌面)
  XXXL: 1536,   // 极大屏幕
}

// 设备类型断点
export const DEVICE_BREAKPOINTS = {
  MOBILE_MAX: BREAKPOINTS.LG - 1,     // 991px - 移动端最大宽度
  TABLET_MIN: BREAKPOINTS.MD,         // 768px - 平板最小宽度
  TABLET_MAX: BREAKPOINTS.XL - 1,     // 1199px - 平板最大宽度
  DESKTOP_MIN: BREAKPOINTS.XL,        // 1200px - 桌面最小宽度
}

// 媒体查询字符串
export const MEDIA_QUERIES = {
  // 最大宽度查询
  MAX_XS: `(max-width: ${BREAKPOINTS.XS - 1}px)`,
  MAX_SM: `(max-width: ${BREAKPOINTS.SM - 1}px)`,
  MAX_MD: `(max-width: ${BREAKPOINTS.MD - 1}px)`,
  MAX_LG: `(max-width: ${BREAKPOINTS.LG - 1}px)`,
  MAX_XL: `(max-width: ${BREAKPOINTS.XL - 1}px)`,
  MAX_XXL: `(max-width: ${BREAKPOINTS.XXL - 1}px)`,
  
  // 最小宽度查询
  MIN_XS: `(min-width: ${BREAKPOINTS.XS}px)`,
  MIN_SM: `(min-width: ${BREAKPOINTS.SM}px)`,
  MIN_MD: `(min-width: ${BREAKPOINTS.MD}px)`,
  MIN_LG: `(min-width: ${BREAKPOINTS.LG}px)`,
  MIN_XL: `(min-width: ${BREAKPOINTS.XL}px)`,
  MIN_XXL: `(min-width: ${BREAKPOINTS.XXL}px)`,
  
  // 范围查询
  MOBILE: `(max-width: ${DEVICE_BREAKPOINTS.MOBILE_MAX}px)`,
  TABLET: `(min-width: ${DEVICE_BREAKPOINTS.TABLET_MIN}px) and (max-width: ${DEVICE_BREAKPOINTS.TABLET_MAX}px)`,
  DESKTOP: `(min-width: ${DEVICE_BREAKPOINTS.DESKTOP_MIN}px)`,
  
  // 特殊查询
  MOBILE_SMALL: `(max-width: ${BREAKPOINTS.SM - 1}px)`,
  MOBILE_LARGE: `(min-width: ${BREAKPOINTS.SM}px) and (max-width: ${DEVICE_BREAKPOINTS.MOBILE_MAX}px)`,
  RETINA: '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',
}

// CSS 变量名称
export const CSS_VARIABLES = {
  MOBILE_MAX_WIDTH: '--responsive-mobile-max-width',
  TABLET_MIN_WIDTH: '--responsive-tablet-min-width',
  TABLET_MAX_WIDTH: '--responsive-tablet-max-width',
  DESKTOP_MIN_WIDTH: '--responsive-desktop-min-width',
}

// 设备类型枚举 (与现有的 DeviceEnum 保持兼容)
export const DEVICE_TYPES = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop',
}

// 容器最大宽度配置
export const CONTAINER_MAX_WIDTHS = {
  SM: BREAKPOINTS.SM,
  MD: BREAKPOINTS.MD,
  LG: BREAKPOINTS.LG,
  XL: BREAKPOINTS.XL,
  XXL: BREAKPOINTS.XXL,
}

// 响应式字体大小配置
export const RESPONSIVE_FONT_SIZES = {
  // 标题字体大小 (rem)
  HEADING: {
    H1: {
      mobile: '1.75rem',    // 28px
      tablet: '2.25rem',    // 36px
      desktop: '3rem',      // 48px
    },
    H2: {
      mobile: '1.5rem',     // 24px
      tablet: '1.875rem',   // 30px
      desktop: '2.25rem',   // 36px
    },
    H3: {
      mobile: '1.25rem',    // 20px
      tablet: '1.5rem',     // 24px
      desktop: '1.875rem',  // 30px
    },
    H4: {
      mobile: '1.125rem',   // 18px
      tablet: '1.25rem',    // 20px
      desktop: '1.5rem',    // 24px
    },
  },
  // 正文字体大小
  BODY: {
    LARGE: {
      mobile: '1rem',       // 16px
      tablet: '1.125rem',   // 18px
      desktop: '1.25rem',   // 20px
    },
    NORMAL: {
      mobile: '0.875rem',   // 14px
      tablet: '1rem',       // 16px
      desktop: '1rem',      // 16px
    },
    SMALL: {
      mobile: '0.75rem',    // 12px
      tablet: '0.875rem',   // 14px
      desktop: '0.875rem',  // 14px
    },
  },
}

// 响应式间距配置
export const RESPONSIVE_SPACING = {
  // 容器内边距
  CONTAINER_PADDING: {
    mobile: '1rem',       // 16px
    tablet: '2rem',       // 32px
    desktop: '4rem',      // 64px
  },
  // 组件间距
  COMPONENT_GAP: {
    mobile: '1rem',       // 16px
    tablet: '1.5rem',     // 24px
    desktop: '2rem',      // 32px
  },
  // 栅格间距
  GRID_GAP: {
    mobile: '0.75rem',    // 12px
    tablet: '1rem',       // 16px
    desktop: '1.5rem',    // 24px
  },
}

// 工具函数：判断当前设备类型
export function getCurrentDeviceType() {
  if (typeof window === 'undefined') return DEVICE_TYPES.DESKTOP
  
  const width = window.innerWidth
  
  if (width <= DEVICE_BREAKPOINTS.MOBILE_MAX) {
    return DEVICE_TYPES.MOBILE
  } else if (width <= DEVICE_BREAKPOINTS.TABLET_MAX) {
    return DEVICE_TYPES.TABLET
  } else {
    return DEVICE_TYPES.DESKTOP
  }
}

// 工具函数：检查是否为移动设备
export function isMobileDevice() {
  return getCurrentDeviceType() === DEVICE_TYPES.MOBILE
}

// 工具函数：检查是否为平板设备
export function isTabletDevice() {
  return getCurrentDeviceType() === DEVICE_TYPES.TABLET
}

// 工具函数：检查是否为桌面设备
export function isDesktopDevice() {
  return getCurrentDeviceType() === DEVICE_TYPES.DESKTOP
}

// 工具函数：获取媒体查询匹配结果
export function matchMedia(query) {
  if (typeof window === 'undefined') return false
  return window.matchMedia(query).matches
}

// 工具函数：创建响应式监听器
export function createResponsiveListener(callback) {
  if (typeof window === 'undefined') return () => {}
  
  const mediaQueries = Object.entries(MEDIA_QUERIES).map(([key, query]) => ({
    key,
    mql: window.matchMedia(query)
  }))
  
  const handleChange = () => {
    const matches = {}
    mediaQueries.forEach(({ key, mql }) => {
      matches[key] = mql.matches
    })
    callback(matches, getCurrentDeviceType())
  }
  
  // 添加监听器
  mediaQueries.forEach(({ mql }) => {
    mql.addListener(handleChange)
  })
  
  // 初始调用
  handleChange()
  
  // 返回清理函数
  return () => {
    mediaQueries.forEach(({ mql }) => {
      mql.removeListener(handleChange)
    })
  }
}

export default {
  BREAKPOINTS,
  DEVICE_BREAKPOINTS,
  MEDIA_QUERIES,
  CSS_VARIABLES,
  DEVICE_TYPES,
  CONTAINER_MAX_WIDTHS,
  RESPONSIVE_FONT_SIZES,
  RESPONSIVE_SPACING,
  getCurrentDeviceType,
  isMobileDevice,
  isTabletDevice,
  isDesktopDevice,
  matchMedia,
  createResponsiveListener,
}
