<template>
  <div class="navigation-bar" :class="{ navigationBarMobile: isMobile }">
    <!-- 小屏菜单展开按钮 -->
    <Hamburger
      v-if="!isTop || isMobile"
      :is-active="appStore.sidebar.opened"
      class="hamburger"
      @toggle-click="toggleSidebar"
    />

    <Logo v-if="isMobile" :isMobile="isMobile" />

    <Breadcrumb v-if="!isTop || isMobile" class="breadcrumb" />
    <Sidebar v-if="isTop && !isMobile" class="sidebar" />
    <div class="right-menu">
      <SearchMenu v-if="showSearchMenu" class="right-menu-item" />
      <ScreenFull v-if="showScreenFull" class="right-menu-item" />
      <ThemeSwitch v-if="showThemeSwitch" class="right-menu-item" />
      <Notify v-if="showNotify" class="right-menu-item" />
    </div>

    <SelectLang v-if="!isMobile"></SelectLang>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router"
import { storeToRefs } from "pinia"
import { useAppStore } from "@/store/modules/app"
import { useSettingsStore } from "@/store/modules/settings"
import { useUserStore } from "@/store/modules/user"
import Hamburger from "../Hamburger/index.vue"
import Breadcrumb from "../Breadcrumb/index.vue"
import Sidebar from "../Sidebar/index.vue"
import Notify from "@/components/Notify/index.vue"
import ThemeSwitch from "@/components/ThemeSwitch/index.vue"
import ScreenFull from "@/components/ScreenFull/index.vue"
import SearchMenu from "@/components/SearchMenu/index.vue"
import { useDevice } from "@/hooks/useDevice"
import { useLayoutMode } from "@/hooks/useLayoutMode"

import Logo from "../Logo/index.vue"

import SelectLang from "../select-lang/index.vue"

import { ref } from "vue"
const { isMobile } = useDevice()
const { isTop } = useLayoutMode()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const { showNotify, showThemeSwitch, showScreenFull, showSearchMenu } = storeToRefs(settingsStore)

const toggleSidebar = () => {
  appStore.toggleSidebar(false)
}
</script>

<style lang="scss" scoped>
.navigation-bar {
  height: var(--v3-navigationbar-height);
  overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 44px;

  // 响应式优化
  @include mobile-only {
    height: 60px;
    padding: 0 15px;
    padding-top: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: var(--el-bg-color);
    position: relative;
    z-index: 1000;
  }

  @include tablet-only {
    padding-top: 20px;
  }

  .lang-box {
    display: flex;
    align-items: center;
    font-size: 12px;
    padding: 0 15px;

    p {
      cursor: pointer;
      color: #666666;

      &:hover {
        font-weight: bold;
      }
    }
  }

  .hamburger {
    display: flex;
    align-items: center;
    height: 60%;
    cursor: pointer;
    position: absolute;
    right: 5%;

    // 响应式优化
    @include mobile-only {
      right: 15px;
      height: 40px;
      width: 40px;
      justify-content: center;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: var(--el-bg-color-page);
      }
    }

    @include tablet-and-desktop {
      right: 5%;
    }
  }

  .breadcrumb {
    flex: 1;

    // 使用统一的响应式断点
    @include mobile-only {
      display: none;
    }
  }

  .sidebar {
    flex: 1;
    // 设置 min-width 是为了让 Sidebar 里的 el-menu 宽度自适应
    min-width: 0px;

    :deep(.el-menu) {
      background-color: transparent;
    }

    :deep(.el-sub-menu) {
      &.is-active {
        .el-sub-menu__title {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }

  .right-menu {
    height: 100%;
    display: flex;
    align-items: center;

    // 响应式优化
    @include mobile-only {
      height: 60px;
      margin-right: 50px; // 为汉堡菜单留出空间
    }

    .right-menu-item {
      padding: 0 10px;
      cursor: pointer;
      transition: background-color 0.3s;
      border-radius: 4px;

      // 响应式优化
      @include mobile-only {
        padding: 0 8px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: var(--el-bg-color-page);
        }
      }

      .right-menu-avatar {
        display: flex;
        align-items: center;

        .el-avatar {
          margin-right: 10px;

          @include mobile-only {
            margin-right: 8px;
            width: 32px;
            height: 32px;
          }
        }

        span {
          font-size: 16px;

          @include mobile-only {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.navigationBarMobile {
  padding: 0 15px;

  // 移动端导航栏特殊样式
  .hamburger {
    right: 15px;
  }

  .right-menu {
    .right-menu-item {
      padding: 0 8px;

      .el-icon {
        font-size: 18px;
      }
    }
  }
}
</style>
